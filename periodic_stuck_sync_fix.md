# Periodic Stuck Sync Fix

## Problem Identified

Both sync managers were getting stuck simultaneously:
- **`ConnectivitySyncService._isSyncing = true`** (from immediate sync after data insertion)
- **`BackgroundSyncManager._isSyncing = true`** (from connectivity change)

The timeout mechanism only worked when a **new sync was attempted**, but if both managers were stuck, no new syncs would be attempted, so the timeout never triggered.

## Root Cause

From your logs:
```
[log] Data inserted - triggering immediate background sync
[log] ConnectivitySyncService: Sync already in progress, skipping...
[log] Internet connection restored  
[log] Background sync already in progress, skipping...
```

Both sync managers were stuck in a deadlock where:
1. ConnectivitySyncService was stuck from a previous sync
2. BackgroundSync<PERSON>anager was stuck from connectivity change
3. Neither could proceed because both thought sync was in progress
4. Timeout only checked when new sync attempted, but no new syncs could start

## Solution: Periodic Stuck Sync Checker

Added **independent periodic timers** that run every 1 minute to check for stuck syncs and auto-reset them.

### 1. ConnectivitySyncService Periodic Checker
```dart
// Added timer field
Timer? _stuckSyncTimer;

// Start periodic checker (every 1 minute)
void _startStuckSyncChecker() {
  _stuckSyncTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
    _checkForStuckSync();
  });
  log('Stuck sync checker started (checks every 1 minute)');
}

// Check and auto-reset stuck syncs
void _checkForStuckSync() {
  if (_isSyncing && _syncStartTime != null) {
    final stuckDuration = DateTime.now().difference(_syncStartTime!);
    if (stuckDuration.inMinutes >= 2) {
      log('ConnectivitySyncService: Detected stuck sync (${stuckDuration.inMinutes} minutes), auto-resetting...');
      _isSyncing = false;
      _syncStartTime = null;
      _statusController.add(SyncStatus.error);
      log('ConnectivitySyncService: Stuck sync auto-reset complete');
    }
  }
}
```

### 2. BackgroundSyncManager Periodic Checker
```dart
// Added timer field
Timer? _stuckSyncTimer;

// Start periodic checker (every 1 minute)
void _startStuckSyncChecker() {
  _stuckSyncTimer?.cancel(); // Cancel any existing timer
  _stuckSyncTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
    _checkForStuckSync();
  });
  log('BackgroundSyncManager: Stuck sync checker started (checks every 1 minute)');
}

// Check and auto-reset stuck syncs
void _checkForStuckSync() {
  if (_isSyncing && _syncStartTime != null) {
    final stuckDuration = DateTime.now().difference(_syncStartTime!);
    if (stuckDuration.inMinutes >= 2) {
      log('BackgroundSyncManager: Detected stuck sync (${stuckDuration.inMinutes} minutes), auto-resetting...');
      _isSyncing = false;
      _syncStartTime = null;
      _lastSyncError = 'Sync timeout - auto-reset';
      log('BackgroundSyncManager: Stuck sync auto-reset complete');
    }
  }
}
```

### 3. Proper Timer Cleanup
```dart
// In dispose methods
void dispose() {
  // ... existing cleanup ...
  _stuckSyncTimer?.cancel();
  // ... rest of cleanup ...
}
```

## How It Works

1. **Both sync managers start periodic timers** when initialized
2. **Every 1 minute**, each timer checks if sync is stuck (>2 minutes)
3. **If stuck**, automatically resets the sync status
4. **Independent operation**: Each manager can reset itself without waiting for the other
5. **No deadlock**: Even if both are stuck, both will auto-reset within 1-3 minutes

## Expected Behavior After Fix

### Your Scenario: Offline → Online → Online Survey
1. **Offline survey created** → Saved with `isUploaded = false`
2. **Go online** → Connectivity sync starts, might get stuck
3. **Create online survey** → Immediate sync starts, might get stuck
4. **Within 1-3 minutes** → Both periodic checkers detect stuck syncs and auto-reset
5. **Next sync attempt** → Works normally, no "already in progress" messages

### Timeline:
- **T+0**: Both syncs get stuck
- **T+1 min**: First periodic check (might not be 2 min stuck yet)
- **T+2 min**: Second periodic check detects 2+ min stuck, auto-resets both
- **T+2 min+**: Next sync attempts work normally

## Testing

### Immediate Testing (for debugging):
```dart
// Force immediate stuck sync check
ConnectivitySyncService.instance.checkStuckSyncNow();
BackgroundSyncManager.instance.checkStuckSyncNow();

// Check current status
ConnectivitySyncService.instance.logConnectivitySyncStatus();
BackgroundSyncManager.instance.logCurrentStatus();
```

### Expected Logs:
- `"Stuck sync checker started (checks every 1 minute)"`
- `"Detected stuck sync (X minutes), auto-resetting..."`
- `"Stuck sync auto-reset complete"`

### Success Indicators:
- No more persistent "already in progress" messages
- Syncs work normally after 1-3 minutes of being stuck
- Both online and offline→online scenarios work

The fix provides **automatic recovery** from stuck sync states without requiring app restart or manual intervention.
