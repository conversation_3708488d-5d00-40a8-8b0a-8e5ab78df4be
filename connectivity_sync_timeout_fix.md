# ConnectivitySyncService Timeout Fix

## Problem Identified

The "ConnectivitySyncService: Sync already in progress, skipping..." issue was caused by the `ConnectivitySyncService._isSyncing` flag getting stuck at `true`.

## Root Cause

When `triggerSyncAfterDataInsertion()` is called (after creating surveys online), it calls `ConnectivitySyncService._performBackgroundSync()` directly. This method has its own `_isSyncing` flag that was getting stuck and never being reset.

### Flow Analysis:
1. **Online survey created** → `triggerSyncAfterDataInsertion()` called
2. **`ConnectivitySyncService._performBackgroundSync()`** → Sets `_isSyncing = true`
3. **Sync delegated to isolate** → Isolate uploads data successfully
4. **Isolate sends completion** → Message handler resets `_isSyncing = false`
5. **BUT if message delivery fails** → `_isSyncing` stays `true` forever
6. **Next sync attempt** → Finds `_isSyncing = true` and skips

## Solution Implemented

### 1. Added Timeout Detection
```dart
if (_isSyncing) {
  log('ConnectivitySyncService: Sync already in progress, skipping...');
  
  // Check if sync has been stuck for too long (more than 2 minutes)
  if (_syncStartTime != null && 
      DateTime.now().difference(_syncStartTime!).inMinutes > 2) {
    log('ConnectivitySyncService: Sync appears to be stuck, forcing reset...');
    _isSyncing = false;
    _syncStartTime = null;
    _statusController.add(SyncStatus.error);
  } else {
    return;
  }
}
```

### 2. Added Sync Start Time Tracking
```dart
// Added field to track when sync started
DateTime? _syncStartTime;

// Set when sync starts
_isSyncing = true;
_syncStartTime = DateTime.now();
_statusController.add(SyncStatus.syncing);
```

### 3. Enhanced Logging
```dart
// Clear distinction between sync services
log('ConnectivitySyncService: Sync already in progress, skipping...');
log('ConnectivitySyncService: _isSyncing = $_isSyncing, _isolateInitialized = $_isolateInitialized');
log('ConnectivitySyncService: Starting sync at $_syncStartTime, delegating to background isolate...');
```

### 4. Added Debug Methods
```dart
// Check ConnectivitySyncService status
ConnectivitySyncService.instance.logConnectivitySyncStatus();

// Force reset if needed (for debugging)
ConnectivitySyncService.instance.forceResetConnectivitySyncStatus();
```

## Expected Behavior After Fix

### Normal Operation:
1. **Survey created online** → `triggerSyncAfterDataInsertion()` called
2. **Sync starts** → `_syncStartTime` set, `_isSyncing = true`
3. **Sync completes** → `_syncStartTime = null`, `_isSyncing = false`
4. **Next survey** → Sync works normally

### Stuck Sync Recovery:
1. **Sync gets stuck** → `_isSyncing` remains `true` for >2 minutes
2. **Next sync attempt** → Detects timeout, auto-resets to `false`
3. **Sync proceeds** → Works normally

### Your Specific Scenario:
1. **Offline survey** → Saved with `isUploaded = false`
2. **Go online** → Connectivity sync uploads data (BackgroundSyncManager works)
3. **Create online survey** → `triggerSyncAfterDataInsertion()` works (ConnectivitySyncService works)
4. **No "already in progress"** → Both sync managers work independently

## Testing

1. **Reproduce your scenario**:
   - Install app, go offline, create survey
   - Go online, wait for sync completion
   - Create another survey online immediately

2. **Expected logs**:
   - `"ConnectivitySyncService: Starting sync at [timestamp]"`
   - `"Data inserted - triggering immediate background sync"`
   - NO "ConnectivitySyncService: Sync already in progress" messages

3. **If stuck (debugging)**:
   ```dart
   ConnectivitySyncService.instance.logConnectivitySyncStatus();
   // Should show _isSyncing = false after timeout
   ```

The fix ensures the ConnectivitySyncService has proper timeout recovery and doesn't get stuck in a syncing state.
