# Immediate Stuck Sync Fix

## Changes Made

Removed the periodic timer approach and implemented an **immediate check-and-reset** mechanism that triggers whenever a sync is attempted while another is already in progress.

## How It Works

### Before (Problematic):
```
1. Sync gets stuck → _isSyncing = true
2. New sync attempt → Checks _isSyncing = true → Skips immediately
3. Sync stays stuck forever
```

### After (Fixed):
```
1. Sync gets stuck → _isSyncing = true, _syncStartTime = [timestamp]
2. New sync attempt → Checks _isSyncing = true
3. Calls checkAndResetIfStuck() → Checks if stuck >1 minute
4. If stuck → Force resets _isSyncing = false
5. Continues with new sync attempt
```

## Implementation

### ConnectivitySyncService
```dart
if (_isSyncing) {
  log('ConnectivitySyncService: Sync already in progress, skipping...');
  
  // Check and reset if stuck
  checkAndResetIfStuck();
  
  // If still syncing after check, return
  if (_isSyncing) {
    return;
  }
}

/// Check and force reset if sync is stuck
void checkAndResetIfStuck() {
  if (_isSyncing && _syncStartTime != null) {
    final stuckDuration = DateTime.now().difference(_syncStartTime!);
    if (stuckDuration.inMinutes >= 1) { // 1 minute threshold
      log('ConnectivitySyncService: Sync stuck for ${stuckDuration.inMinutes} minutes, force resetting...');
      forceResetConnectivitySyncStatus();
    }
  }
}
```

### BackgroundSyncManager
```dart
if (_isSyncing) {
  log('Background sync already in progress, skipping...');
  logCurrentStatus();

  // Check and reset if stuck
  checkAndResetIfStuck();

  // If still syncing after check, return
  if (_isSyncing) {
    return;
  }
}

/// Check and force reset if sync is stuck
void checkAndResetIfStuck() {
  if (_isSyncing && _syncStartTime != null) {
    final stuckDuration = DateTime.now().difference(_syncStartTime!);
    if (stuckDuration.inMinutes >= 1) { // 1 minute threshold
      log('BackgroundSyncManager: Sync stuck for ${stuckDuration.inMinutes} minutes, force resetting...');
      forceResetSyncStatus();
    }
  }
}
```

## Key Improvements

1. **Immediate Detection**: No waiting for periodic timers - stuck syncs are detected immediately when new sync is attempted
2. **Reduced Timeout**: Changed from 2 minutes to 1 minute for faster recovery
3. **Simpler Logic**: No complex timer management, just immediate check-and-reset
4. **Both Managers**: Applied to both ConnectivitySyncService and BackgroundSyncManager

## Expected Behavior

### Your Scenario: Offline → Online → Online Survey
1. **Offline survey created** → Saved with `isUploaded = false`
2. **Go online** → Connectivity sync starts, might get stuck
3. **Create online survey** → Immediate sync attempts
4. **Stuck sync detected** → `checkAndResetIfStuck()` detects >1 min, force resets
5. **Sync proceeds** → New sync starts immediately after reset
6. **No more "already in progress"** → Syncs work normally

### Timeline:
- **T+0**: Sync gets stuck
- **T+1 min+**: Next sync attempt detects stuck sync and immediately resets
- **T+1 min+**: New sync starts immediately

## Testing

### Expected Logs:
- `"ConnectivitySyncService: Sync stuck for X minutes, force resetting..."`
- `"BackgroundSyncManager: Sync stuck for X minutes, force resetting..."`
- `"Sync status reset complete"`

### Success Indicators:
- Syncs that were stuck for >1 minute get immediately reset on next attempt
- No persistent "already in progress" messages
- Both online and offline→online scenarios work
- Faster recovery (1 minute vs 2+ minutes)

### Manual Testing (if needed):
```dart
// Check current status
ConnectivitySyncService.instance.logConnectivitySyncStatus();
BackgroundSyncManager.instance.logCurrentStatus();

// Force reset if needed
ConnectivitySyncService.instance.forceResetConnectivitySyncStatus();
BackgroundSyncManager.instance.forceResetSyncStatus();
```

The fix provides immediate recovery from stuck sync states as soon as a new sync is attempted, eliminating the deadlock situation.
