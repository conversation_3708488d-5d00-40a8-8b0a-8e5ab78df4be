# Isolate Technical Deep Dive - Survey Background Sync

## Memory Model and Data Sharing

### Isolate Memory Isolation

```
┌─────────────────────────────────────────────────────────────────┐
│                        Main Isolate Memory                      │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ UI State        │  │ Widget Tree     │  │ App State       │ │
│  │ - Survey Forms  │  │ - Rendered UI   │  │ - User Session  │ │
│  │ - User Input    │  │ - Event Queue   │  │ - Navigation    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │              Sync Manager & Service                         │ │
│  │  - SendPort/ReceivePort references                          │ │
│  │  - Stream controllers                                       │ │
│  │  - Status tracking variables                                │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                   │
                            Message Passing
                            (Serialized Data)
                                   │
                                   ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Background Isolate Memory                    │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    _SyncWorker                              │ │
│  │  - SurveyService instance                                   │ │
│  │  - S3UploadService instance                                 │ │
│  │  - SendPort reference to main isolate                      │ │
│  │  - Local variables for sync operations                     │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │              Database & Network Operations                   │ │
│  │  - Hive box instances                                       │ │
│  │  - HTTP client instances                                    │ │
│  │  - Temporary data structures                                │ │
│  │  - Processing buffers                                       │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### Data Serialization Rules

**✅ Can be passed between isolates:**
```dart
// Primitive types
int, double, String, bool, null

// Collections of primitives
List<String>, Map<String, dynamic>, Set<int>

// Simple structured data
{
  'command': 'start_survey_sync',
  'timestamp': 1234567890,
  'data': ['item1', 'item2']
}
```

**❌ Cannot be passed between isolates:**
```dart
// Complex objects
SurveyModel, BuildContext, Widget

// Functions and closures
() => doSomething(), callback functions

// Platform-specific objects
File, Socket, Database connections

// Objects with non-serializable fields
class ComplexObject {
  final Function callback; // ❌ Cannot serialize
  final BuildContext context; // ❌ Cannot serialize
}
```

## Communication Protocol Implementation

### 1. Bidirectional Setup Sequence

```dart
// Step-by-step communication setup

// STEP 1: Main isolate creates ReceivePort
final mainReceivePort = ReceivePort();

// STEP 2: Main isolate spawns background isolate
final isolate = await Isolate.spawn(
  backgroundEntryPoint,
  mainReceivePort.sendPort, // Pass main's SendPort to background
);

// STEP 3: Background isolate entry point
void backgroundEntryPoint(SendPort mainSendPort) async {
  // Create ReceivePort for background isolate
  final backgroundReceivePort = ReceivePort();
  
  // Send background's SendPort to main isolate
  mainSendPort.send(backgroundReceivePort.sendPort);
  
  // Now both isolates can communicate bidirectionally
}

// STEP 4: Main isolate receives background's SendPort
late SendPort backgroundSendPort;
mainReceivePort.listen((message) {
  if (message is SendPort) {
    backgroundSendPort = message;
    // Communication is now established
  }
});
```

### 2. Message Queue and Processing

```dart
// Background isolate message processing loop
await for (final message in backgroundReceivePort) {
  if (message is Map<String, dynamic>) {
    final String command = message['command'] ?? '';
    
    // Process commands asynchronously
    switch (command) {
      case 'start_survey_sync':
        // Don't await - allows processing multiple commands
        _processSurveySync(message);
        break;
        
      case 'start_image_sync':
        _processImageSync(message);
        break;
        
      case 'cancel_sync':
        _cancelCurrentOperations();
        break;
        
      case 'stop':
        backgroundReceivePort.close();
        return; // Exit isolate
    }
  }
}
```

### 3. Error Handling and Recovery

```dart
// Robust error handling in background isolate
Future<void> _processSurveySync(Map<String, dynamic> command) async {
  try {
    _sendStatus('syncing');
    
    // Heavy operations
    await _performSyncOperations();
    
    _sendStatus('completed');
  } catch (e) {
    // Send error to main isolate
    _sendError('Sync failed: $e');
    _sendStatus('error');
    
    // Log for debugging
    _log('Error in survey sync: $e');
    _log('Stack trace: ${StackTrace.current}');
  }
}

// Main isolate error handling
void _handleIsolateMessage(dynamic message) {
  try {
    if (message is Map<String, dynamic>) {
      final String type = message['type'] ?? '';
      
      switch (type) {
        case 'error':
          final String error = message['error'] ?? 'Unknown error';
          _handleSyncError(error);
          break;
          
        case 'status':
          _updateSyncStatus(message['status']);
          break;
      }
    }
  } catch (e) {
    log('Error handling isolate message: $e');
    // Don't let message handling errors crash the app
  }
}
```

## Performance Optimization Strategies

### 1. Batch Processing

```dart
// Process records in batches to avoid overwhelming the system
Future<void> syncSurveyData() async {
  const int batchSize = 10;
  final box = await Hive.openBox('surveyBox');
  final keys = box.keys.toList();
  
  for (int i = 0; i < keys.length; i += batchSize) {
    final batchEnd = math.min(i + batchSize, keys.length);
    final batch = keys.sublist(i, batchEnd);
    
    // Process batch
    await _processBatch(batch, box);
    
    // Update progress
    _sendProgress(batchEnd, keys.length, 'Syncing survey data');
    
    // Yield control to allow other operations
    await Future.delayed(Duration.zero);
  }
}
```

### 2. Memory Management

```dart
// Efficient memory usage in background isolate
Future<void> _processBatch(List<dynamic> keys, Box box) async {
  for (final key in keys) {
    try {
      // Get data
      final data = box.get(key);
      if (data == null) continue;
      
      // Process immediately and release reference
      final result = await _processRecord(data);
      
      if (result) {
        // Update record
        final updatedData = Map<String, dynamic>.from(data);
        updatedData['isUploaded'] = true;
        await box.put(key, updatedData);
      }
      
      // Clear local references to help GC
      // (Dart GC will handle this automatically, but good practice)
      
    } catch (e) {
      _log('Error processing record $key: $e');
    }
  }
}
```

### 3. Network Optimization

```dart
// Efficient network operations
class _SyncWorker {
  // Reuse HTTP client across requests
  static final http.Client _httpClient = http.Client();
  
  Future<bool> _syncPoleSurvey(Map<String, dynamic> surveyData) async {
    try {
      // Use persistent connection
      final response = await _httpClient.post(
        Uri.parse(ApiEndpoints.updateSurvey),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(surveyData),
      );
      
      return response.statusCode == 200;
    } catch (e) {
      _log('Network error: $e');
      return false;
    }
  }
  
  // Cleanup when isolate terminates
  void dispose() {
    _httpClient.close();
  }
}
```

## Debugging and Monitoring

### 1. Comprehensive Logging

```dart
// Enhanced logging system
class IsolateLogger {
  static void log(String message, {String? level, Map<String, dynamic>? context}) {
    final logEntry = {
      'type': 'log',
      'level': level ?? 'INFO',
      'message': message,
      'timestamp': DateTime.now().toIso8601String(),
      'isolate': 'background',
      'context': context,
    };
    
    _sendMessage(logEntry);
  }
  
  static void error(String message, dynamic error, StackTrace? stackTrace) {
    log(
      message,
      level: 'ERROR',
      context: {
        'error': error.toString(),
        'stackTrace': stackTrace?.toString(),
      },
    );
  }
  
  static void performance(String operation, int durationMs, {Map<String, dynamic>? metrics}) {
    log(
      'Performance: $operation completed in ${durationMs}ms',
      level: 'PERF',
      context: {
        'operation': operation,
        'duration_ms': durationMs,
        'metrics': metrics,
      },
    );
  }
}
```

### 2. Performance Monitoring

```dart
// Performance tracking in background operations
Future<void> syncSurveyData() async {
  final stopwatch = Stopwatch()..start();
  int recordsProcessed = 0;
  int networkCalls = 0;
  int errors = 0;
  
  try {
    _sendStatus(SyncStatus.syncing);
    
    final box = await Hive.openBox('surveyBox');
    final totalRecords = box.keys.length;
    
    for (final key in box.keys) {
      final recordStopwatch = Stopwatch()..start();
      
      try {
        final data = box.get(key);
        if (data == null) continue;
        
        final result = await _processRecord(data);
        networkCalls++;
        
        if (result) {
          recordsProcessed++;
        }
        
      } catch (e) {
        errors++;
        IsolateLogger.error('Record processing failed', e, StackTrace.current);
      }
      
      recordStopwatch.stop();
      
      // Log slow operations
      if (recordStopwatch.elapsedMilliseconds > 5000) {
        IsolateLogger.log(
          'Slow record processing detected',
          level: 'WARN',
          context: {
            'key': key.toString(),
            'duration_ms': recordStopwatch.elapsedMilliseconds,
          },
        );
      }
    }
    
    stopwatch.stop();
    
    // Send performance metrics
    IsolateLogger.performance(
      'Survey sync',
      stopwatch.elapsedMilliseconds,
      metrics: {
        'total_records': totalRecords,
        'processed_records': recordsProcessed,
        'network_calls': networkCalls,
        'errors': errors,
        'success_rate': recordsProcessed / totalRecords,
      },
    );
    
    _sendStatus(SyncStatus.completed);
    
  } catch (e) {
    stopwatch.stop();
    IsolateLogger.error('Survey sync failed', e, StackTrace.current);
    _sendStatus(SyncStatus.error);
  }
}
```

### 3. Health Monitoring

```dart
// Isolate health monitoring
class IsolateHealthMonitor {
  static Timer? _healthTimer;
  static DateTime _lastHeartbeat = DateTime.now();
  
  static void startHealthMonitoring(SendPort mainSendPort) {
    _healthTimer = Timer.periodic(Duration(seconds: 30), (timer) {
      _sendHeartbeat(mainSendPort);
    });
  }
  
  static void _sendHeartbeat(SendPort mainSendPort) {
    _lastHeartbeat = DateTime.now();
    
    mainSendPort.send({
      'type': 'heartbeat',
      'timestamp': _lastHeartbeat.toIso8601String(),
      'memory_usage': _getMemoryUsage(),
      'uptime_seconds': _getUptimeSeconds(),
    });
  }
  
  static Map<String, dynamic> _getMemoryUsage() {
    // Platform-specific memory monitoring
    return {
      'rss_bytes': ProcessInfo.currentRss,
      'max_rss_bytes': ProcessInfo.maxRss,
    };
  }
  
  static void stopHealthMonitoring() {
    _healthTimer?.cancel();
    _healthTimer = null;
  }
}
```

## Troubleshooting Common Issues

### 1. Isolate Spawn Failures

```dart
// Robust isolate initialization with fallback
Future<void> initialize() async {
  int retryCount = 0;
  const maxRetries = 3;
  
  while (retryCount < maxRetries) {
    try {
      _syncIsolate = await Isolate.spawn(
        _syncIsolateEntryPoint,
        _syncReceivePort!.sendPort,
      );
      
      // Success
      break;
      
    } catch (e) {
      retryCount++;
      log('Isolate spawn attempt $retryCount failed: $e');
      
      if (retryCount >= maxRetries) {
        // Fallback to main thread processing
        log('All isolate spawn attempts failed, falling back to main thread');
        _useFallbackSync = true;
        return;
      }
      
      // Wait before retry
      await Future.delayed(Duration(seconds: retryCount));
    }
  }
}
```

### 2. Communication Timeouts

```dart
// Timeout handling for isolate communication
Future<void> startSurveySync() async {
  try {
    _syncSendPort?.send({
      'command': 'start_survey_sync',
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
    
    // Wait for acknowledgment with timeout
    final ackReceived = await _waitForAcknowledgment().timeout(
      Duration(seconds: 10),
      onTimeout: () {
        log('Isolate communication timeout - isolate may be unresponsive');
        return false;
      },
    );
    
    if (!ackReceived) {
      // Restart isolate
      await _restartIsolate();
    }
    
  } catch (e) {
    log('Communication error: $e');
  }
}
```

### 3. Memory Leaks Prevention

```dart
// Proper cleanup to prevent memory leaks
class BackgroundSurveySyncService {
  void dispose() {
    // Cancel all timers
    _periodicSyncTimer?.cancel();
    
    // Close all streams
    _progressController.close();
    _statusController.close();
    
    // Cancel subscriptions
    _statusSubscription?.cancel();
    _progressSubscription?.cancel();
    
    // Kill isolate
    _syncIsolate?.kill(priority: Isolate.immediate);
    
    // Close ports
    _syncReceivePort?.close();
    
    // Clear references
    _syncIsolate = null;
    _syncSendPort = null;
    _syncReceivePort = null;
    
    // Reset state
    _isInitialized = false;
    _isSyncing = false;
  }
}
```

This technical deep dive provides the detailed implementation knowledge needed to understand, maintain, and troubleshoot the isolate-based background sync system.
