# Isolate Quick Reference Guide

## Project Structure

```
lib/survey/
├── background_survey_sync_service.dart    # Core isolate service
├── background_sync_manager.dart           # UI integration layer
├── widgets/
│   └── sync_status_indicator.dart         # UI components
└── screens/
    ├── base_survey.dart                   # Updated to use background sync
    ├── pole_survey.dart                   # Updated to use background sync
    ├── switch_point_survey.dart           # Updated to use background sync
    └── transformer_survey.dart            # Updated to use background sync

test/survey/
├── background_sync_test.dart              # Unit tests
└── background_sync_integration_test.dart  # Integration tests

docs/
├── isolate_implementation_guide.md        # Comprehensive guide
├── isolate_technical_deep_dive.md         # Technical details
└── isolate_quick_reference.md             # This file
```

## Key Classes and Their Roles

### 1. BackgroundSurveySyncService
**Purpose**: Core isolate management and communication
**Location**: `lib/survey/background_survey_sync_service.dart`

```dart
// Singleton service that manages background isolate
final service = BackgroundSurveySyncService.instance;

// Initialize isolate
await service.initialize();

// Start background sync
await service.startSurveySync();
await service.startImageSync();

// Listen to progress
service.progressStream.listen((progress) {
  print('Progress: ${progress.percentage}%');
});

// Listen to status
service.statusStream.listen((status) {
  print('Status: ${status.name}');
});

// Cleanup
service.dispose();
```

### 2. BackgroundSyncManager
**Purpose**: High-level interface for UI components
**Location**: `lib/survey/background_sync_manager.dart`

```dart
// Easy-to-use manager for UI integration
final manager = BackgroundSyncManager.instance;

// Initialize with periodic sync
await manager.initialize();

// Manual sync trigger
await manager.startBackgroundSync();

// Get status info
final info = manager.syncStatusInfo;
print('Last sync: ${info.statusText}');

// Cleanup
manager.dispose();
```

### 3. SyncStatusIndicator
**Purpose**: UI components for showing sync status
**Location**: `lib/survey/widgets/sync_status_indicator.dart`

```dart
// Compact indicator for app bars
const CompactSyncStatusIndicator()

// Full indicator with progress
const SyncStatusIndicator(
  showProgress: true,
  showText: true,
)

// Detailed card for settings
DetailedSyncStatusCard(
  onManualSync: () => manager.startBackgroundSync(),
)
```

## Message Types

### Commands (Main → Background)
```dart
// Start survey data sync
{
  'command': 'start_survey_sync',
  'timestamp': 1234567890
}

// Start image sync
{
  'command': 'start_image_sync',
  'timestamp': 1234567890
}

// Stop isolate
{
  'command': 'stop'
}
```

### Responses (Background → Main)
```dart
// Progress update
{
  'type': 'progress',
  'current': 25,
  'total': 100,
  'operation': 'Syncing survey data',
  'percentage': 25
}

// Status change
{
  'type': 'status',
  'status': 'syncing' // idle, syncing, completed, error
}

// Log message
{
  'type': 'log',
  'message': 'Processing record 25 of 100'
}

// Error notification
{
  'type': 'error',
  'error': 'Network connection failed'
}
```

## Common Usage Patterns

### 1. Initialize Background Sync in App Startup
```dart
class MyApp extends StatefulWidget {
  @override
  void initState() {
    super.initState();
    _initializeBackgroundSync();
  }
  
  Future<void> _initializeBackgroundSync() async {
    try {
      await BackgroundSyncManager.instance.initialize();
    } catch (e) {
      print('Failed to initialize background sync: $e');
    }
  }
}
```

### 2. Trigger Sync on Connectivity Change
```dart
void _onConnectivityChanged(ConnectivityResult result) {
  if (result != ConnectivityResult.none) {
    // Internet restored - start background sync
    BackgroundSyncManager.instance.startBackgroundSync();
  }
}
```

### 3. Show Sync Status in UI
```dart
AppBar(
  title: Text('Survey'),
  actions: [
    const CompactSyncStatusIndicator(),
    const SizedBox(width: 8),
  ],
)
```

### 4. Listen to Sync Progress
```dart
class SyncProgressWidget extends StatefulWidget {
  @override
  void initState() {
    super.initState();
    
    BackgroundSyncManager.instance.progressStream.listen((progress) {
      setState(() {
        _currentProgress = progress;
      });
    });
  }
}
```

## Error Handling Patterns

### 1. Isolate Initialization Failure
```dart
try {
  await BackgroundSyncManager.instance.initialize();
} catch (e) {
  // Fallback to main thread sync or show error
  print('Background sync unavailable: $e');
}
```

### 2. Sync Operation Failure
```dart
BackgroundSyncManager.instance.statusStream.listen((status) {
  if (status == SyncStatus.error) {
    // Show error message to user
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Sync failed - will retry later')),
    );
  }
});
```

### 3. Communication Timeout
```dart
// Service handles timeouts internally and restarts isolate if needed
// UI components should listen to status stream for updates
```

## Performance Guidelines

### ✅ Do's
- Use singleton pattern for sync services
- Initialize background sync early in app lifecycle
- Listen to streams for real-time updates
- Batch process large datasets
- Implement proper cleanup in dispose methods
- Use structured message protocols

### ❌ Don'ts
- Don't create multiple isolates for the same purpose
- Don't pass complex objects between isolates
- Don't block the main thread waiting for isolate responses
- Don't ignore error handling
- Don't forget to dispose resources

## Testing Strategies

### 1. Unit Tests
```dart
test('BackgroundSyncManager should be singleton', () {
  final instance1 = BackgroundSyncManager.instance;
  final instance2 = BackgroundSyncManager.instance;
  expect(instance1, equals(instance2));
});
```

### 2. Integration Tests
```dart
testWidgets('SyncStatusIndicator should display correctly', (tester) async {
  await tester.pumpWidget(
    MaterialApp(home: CompactSyncStatusIndicator()),
  );
  
  expect(find.byType(CompactSyncStatusIndicator), findsOneWidget);
});
```

### 3. Performance Tests
```dart
test('Sync initialization should complete quickly', () async {
  final stopwatch = Stopwatch()..start();
  
  try {
    await BackgroundSyncManager.instance.initialize();
    stopwatch.stop();
    expect(stopwatch.elapsedMilliseconds, lessThan(15000));
  } catch (e) {
    // Expected in test environment
  }
});
```

## Debugging Tips

### 1. Enable Detailed Logging
```dart
// Logs are automatically sent from isolate to main thread
// Check console for messages prefixed with "Isolate:"
```

### 2. Monitor Sync Status
```dart
// Add temporary logging to track sync progress
BackgroundSyncManager.instance.statusStream.listen((status) {
  print('Sync status changed to: ${status.name}');
});

BackgroundSyncManager.instance.progressStream.listen((progress) {
  print('Sync progress: ${progress.current}/${progress.total}');
});
```

### 3. Check Isolate Health
```dart
// Service automatically handles isolate failures and restarts
// Monitor status stream for error states
```

## Migration from Main Thread Sync

### Before (Main Thread - Blocking)
```dart
Timer.periodic(Duration(minutes: 10), (timer) {
  uploadSurveyData(context);  // Blocks UI
  uploadCapturedImages(context);  // Blocks UI
});
```

### After (Background Isolate - Non-blocking)
```dart
// Initialize once
await BackgroundSyncManager.instance.initialize();

// Automatic periodic sync happens in background
// Manual trigger when needed
BackgroundSyncManager.instance.startBackgroundSync();
```

## File Locations Summary

| Component | File Path | Purpose |
|-----------|-----------|---------|
| Core Service | `lib/survey/background_survey_sync_service.dart` | Isolate management |
| Manager | `lib/survey/background_sync_manager.dart` | UI integration |
| UI Components | `lib/survey/widgets/sync_status_indicator.dart` | Status display |
| Tests | `test/survey/background_sync_*.dart` | Testing |
| Documentation | `docs/isolate_*.md` | This documentation |

## Quick Troubleshooting

| Issue | Likely Cause | Solution |
|-------|--------------|----------|
| Sync not starting | Not initialized | Call `manager.initialize()` |
| UI still blocking | Using old sync methods | Remove timer-based uploads |
| No progress updates | Not listening to streams | Add stream listeners |
| Memory leaks | Not disposing | Call `dispose()` in cleanup |
| Isolate crashes | Unhandled errors | Check error logs |

This quick reference provides the essential information needed to work with the isolate-based background sync system.
