# Isolate Implementation Guide for Background Survey Sync

## Table of Contents
1. [Overview](#overview)
2. [Isolate Architecture](#isolate-architecture)
3. [Communication Flow](#communication-flow)
4. [Implementation Details](#implementation-details)
5. [Message Protocol](#message-protocol)
6. [Error Handling](#error-handling)
7. [Performance Considerations](#performance-considerations)
8. [Best Practices](#best-practices)

## Overview

This project uses **Dart Isolates** to implement background survey data synchronization, preventing UI blocking during bulk upload operations. Isolates provide true parallelism by running in separate memory spaces, ensuring the main UI thread remains responsive.

### Why Isolates?

- **True Parallelism**: Unlike async/await which runs on the main thread, isolates run in separate threads
- **Memory Isolation**: Each isolate has its own memory heap, preventing interference
- **UI Responsiveness**: Main thread remains free for UI operations
- **Crash Isolation**: If background sync fails, it doesn't crash the main app

## Isolate Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        Main Isolate (UI Thread)                 │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────┐    ┌─────────────────────────────────┐ │
│  │ BackgroundSyncManager│    │ BackgroundSurveySyncService     │ │
│  │                     │    │                                 │ │
│  │ - UI Interface      │    │ - Isolate Management           │ │
│  │ - Status Tracking   │    │ - Communication Setup          │ │
│  │ - Progress Streams  │    │ - Message Handling             │ │
│  └─────────────────────┘    └─────────────────────────────────┘ │
│                                        │                        │
│                                        │ SendPort/ReceivePort   │
│                                        ▼                        │
├─────────────────────────────────────────────────────────────────┤
│                     Communication Layer                         │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │              Message Passing Protocol                       │ │
│  │  - Command Messages (start_survey_sync, start_image_sync)   │ │
│  │  - Status Updates (syncing, completed, error)              │ │
│  │  - Progress Reports (current/total/percentage)             │ │
│  │  - Log Messages                                            │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                   │
                                   ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Background Isolate (Worker Thread)           │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    _SyncWorker                              │ │
│  │                                                             │ │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │ │
│  │  │ Survey Data     │  │ Image Upload    │  │ Progress    │ │ │
│  │  │ Sync            │  │ Processing      │  │ Reporting   │ │ │
│  │  │                 │  │                 │  │             │ │ │
│  │  │ - Hive DB       │  │ - S3 Upload     │  │ - Status    │ │ │
│  │  │ - API Calls     │  │ - Base64 Proc   │  │ - Logs      │ │ │
│  │  │ - Error Handle  │  │ - Retry Logic   │  │ - Metrics   │ │ │
│  │  └─────────────────┘  └─────────────────┘  └─────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## Communication Flow

### 1. Isolate Initialization

```dart
// Main Isolate
_syncReceivePort = ReceivePort();
_syncIsolate = await Isolate.spawn(
  _syncIsolateEntryPoint,
  _syncReceivePort!.sendPort,
);

// Background Isolate
static void _syncIsolateEntryPoint(SendPort mainSendPort) async {
  final isolateReceivePort = ReceivePort();
  mainSendPort.send(isolateReceivePort.sendPort); // Send back SendPort
  
  final worker = _SyncWorker(mainSendPort);
  // Listen for commands...
}
```

### 2. Bidirectional Communication Setup

```
Main Isolate                    Background Isolate
     │                               │
     │ 1. Spawn isolate              │
     │ ──────────────────────────────▶│
     │                               │
     │ 2. Send ReceivePort.sendPort  │
     │ ◀──────────────────────────────│
     │                               │
     │ 3. Store SendPort for commands│
     │                               │
     │ 4. Send sync command          │
     │ ──────────────────────────────▶│
     │                               │
     │ 5. Receive progress updates   │
     │ ◀──────────────────────────────│
     │                               │
     │ 6. Receive completion status  │
     │ ◀──────────────────────────────│
```

### 3. Message Flow During Sync

```dart
// Command from Main to Background
{
  'command': 'start_survey_sync',
  'timestamp': DateTime.now().millisecondsSinceEpoch,
}

// Progress from Background to Main
{
  'type': 'progress',
  'current': 25,
  'total': 100,
  'operation': 'Syncing survey data',
  'percentage': 25,
}

// Status from Background to Main
{
  'type': 'status',
  'status': 'completed',
}
```

## Implementation Details

### 1. Isolate Lifecycle Management

```dart
class BackgroundSurveySyncService {
  Isolate? _syncIsolate;
  SendPort? _syncSendPort;
  ReceivePort? _syncReceivePort;
  
  Future<void> initialize() async {
    // 1. Create communication channel
    _syncReceivePort = ReceivePort();
    _syncReceivePort!.listen(_handleIsolateMessage);
    
    // 2. Spawn isolate with entry point
    _syncIsolate = await Isolate.spawn(
      _syncIsolateEntryPoint,
      _syncReceivePort!.sendPort,
    );
    
    // 3. Wait for isolate to send back its SendPort
    final Completer<SendPort> completer = Completer<SendPort>();
    // ... setup bidirectional communication
  }
  
  void dispose() {
    _syncIsolate?.kill(priority: Isolate.immediate);
    _syncReceivePort?.close();
  }
}
```

### 2. Worker Implementation in Background Isolate

```dart
class _SyncWorker {
  final SendPort _mainSendPort;
  final SurveyService _surveyService = SurveyService();
  
  Future<void> syncSurveyData() async {
    try {
      _sendStatus(SyncStatus.syncing);
      
      // Heavy operations that would block UI
      final box = await Hive.openBox('surveyBox');
      final totalRecords = box.keys.length;
      
      for (int i = 0; i < totalRecords; i++) {
        // Process each record
        _sendProgress(i + 1, totalRecords, 'Syncing survey data');
        
        // Network calls, database operations
        await _syncSingleRecord(box.getAt(i));
      }
      
      _sendStatus(SyncStatus.completed);
    } catch (e) {
      _sendError('Sync failed: $e');
    }
  }
}
```

### 3. Message Handling in Main Isolate

```dart
void _handleIsolateMessage(dynamic message) {
  if (message is Map<String, dynamic>) {
    final String type = message['type'] ?? '';
    
    switch (type) {
      case 'progress':
        final progress = SyncProgress.fromMap(message);
        _progressController.add(progress);
        break;
        
      case 'status':
        final status = SyncStatus.values.firstWhere(
          (s) => s.name == message['status'],
        );
        _statusController.add(status);
        break;
        
      case 'log':
        log('Isolate: ${message['message']}');
        break;
    }
  }
}
```

## Message Protocol

### Command Messages (Main → Background)

| Command | Purpose | Parameters |
|---------|---------|------------|
| `start_survey_sync` | Begin survey data sync | `timestamp` |
| `start_image_sync` | Begin image upload sync | `timestamp` |
| `stop` | Terminate isolate | None |

### Response Messages (Background → Main)

| Type | Purpose | Fields |
|------|---------|--------|
| `progress` | Sync progress update | `current`, `total`, `operation`, `percentage` |
| `status` | Sync status change | `status` (idle/syncing/completed/error) |
| `log` | Debug/info logging | `message` |
| `error` | Error notification | `error` |

### Message Structure Examples

```dart
// Progress Message
{
  'type': 'progress',
  'current': 45,
  'total': 100,
  'operation': 'Uploading images',
  'percentage': 45
}

// Status Message
{
  'type': 'status',
  'status': 'syncing' // or 'completed', 'error', 'idle'
}

// Error Message
{
  'type': 'error',
  'error': 'Network connection failed'
}
```

## Error Handling

### 1. Isolate Spawn Failures
```dart
try {
  _syncIsolate = await Isolate.spawn(/*...*/);
} catch (e) {
  log('Failed to spawn isolate: $e');
  _cleanup();
  rethrow;
}
```

### 2. Communication Failures
```dart
void _sendMessage(Map<String, dynamic> message) {
  try {
    _mainSendPort.send(message);
  } catch (e) {
    // Isolate communication failed - log but don't crash
    // Main isolate will handle timeout
  }
}
```

### 3. Background Operation Failures
```dart
Future<void> syncSurveyData() async {
  try {
    // Sync operations
  } catch (e) {
    _sendError('Sync failed: $e');
    _sendStatus(SyncStatus.error);
  }
}
```

## Performance Considerations

### 1. Memory Management
- **Isolate Memory**: Each isolate has ~2MB overhead
- **Data Sharing**: Only primitives and basic collections can be passed
- **Large Data**: Avoid passing large objects between isolates

### 2. CPU Usage
- **Background Priority**: Isolates run at normal priority
- **Batch Processing**: Process records in batches to avoid overwhelming
- **Yield Control**: Use `await` to yield control periodically

### 3. Resource Cleanup
```dart
void dispose() {
  // Always cleanup resources
  _syncIsolate?.kill(priority: Isolate.immediate);
  _syncReceivePort?.close();
  _progressController.close();
  _statusController.close();
}
```

## Best Practices

### 1. Isolate Design
- ✅ Keep isolates lightweight and focused
- ✅ Use singleton pattern for isolate services
- ✅ Implement proper cleanup and disposal
- ❌ Don't create too many isolates (resource intensive)

### 2. Communication
- ✅ Use structured message protocols
- ✅ Handle communication failures gracefully
- ✅ Implement timeouts for isolate operations
- ❌ Don't pass complex objects between isolates

### 3. Error Handling
- ✅ Isolate errors separately from main thread
- ✅ Provide fallback mechanisms
- ✅ Log errors for debugging
- ❌ Don't let isolate errors crash the main app

### 4. Testing
- ✅ Test isolate initialization and cleanup
- ✅ Test message passing protocols
- ✅ Test error scenarios
- ✅ Test performance under load

## Debugging Isolates

### 1. Logging Strategy
```dart
// In isolate
void _log(String message) {
  _sendMessage({
    'type': 'log',
    'message': '[ISOLATE] $message',
    'timestamp': DateTime.now().toIso8601String(),
  });
}
```

### 2. Performance Monitoring
```dart
// Track isolate performance
final stopwatch = Stopwatch()..start();
await syncOperation();
stopwatch.stop();
_log('Sync completed in ${stopwatch.elapsedMilliseconds}ms');
```

### 3. Memory Monitoring
```dart
// Monitor memory usage (development only)
import 'dart:developer' as developer;
developer.log('Memory usage: ${ProcessInfo.currentRss}');
```

This isolate implementation ensures that survey data synchronization happens efficiently in the background without blocking the UI, providing a smooth user experience even during bulk upload operations.

## Code Examples and Implementation Details

### Complete Isolate Setup Example

```dart
// File: lib/survey/background_survey_sync_service.dart

class BackgroundSurveySyncService {
  static BackgroundSurveySyncService? _instance;
  static BackgroundSurveySyncService get instance => _instance ??= BackgroundSurveySyncService._();

  // Isolate management
  Isolate? _syncIsolate;
  SendPort? _syncSendPort;
  ReceivePort? _syncReceivePort;

  // State management
  bool _isSyncing = false;
  bool _isInitialized = false;

  // Communication streams
  final StreamController<SyncProgress> _progressController = StreamController<SyncProgress>.broadcast();
  final StreamController<SyncStatus> _statusController = StreamController<SyncStatus>.broadcast();

  Stream<SyncProgress> get progressStream => _progressController.stream;
  Stream<SyncStatus> get statusStream => _statusController.stream;

  /// Initialize the background sync service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      log('Initializing BackgroundSurveySyncService...');

      // Step 1: Create receive port for communication with isolate
      _syncReceivePort = ReceivePort();

      // Step 2: Listen for messages from isolate
      _syncReceivePort!.listen(_handleIsolateMessage);

      // Step 3: Spawn the sync isolate with entry point function
      _syncIsolate = await Isolate.spawn(
        _syncIsolateEntryPoint,  // Entry point function
        _syncReceivePort!.sendPort,  // Pass main isolate's SendPort
      );

      // Step 4: Wait for isolate to send back its SendPort
      final Completer<SendPort> completer = Completer<SendPort>();
      late StreamSubscription subscription;

      subscription = _syncReceivePort!.listen((message) {
        if (message is SendPort) {
          _syncSendPort = message;
          completer.complete(message);
          subscription.cancel();
        }
      });

      // Step 5: Wait for bidirectional communication setup (with timeout)
      await completer.future.timeout(const Duration(seconds: 10));

      _isInitialized = true;
      log('BackgroundSurveySyncService initialized successfully');

    } catch (e) {
      log('Failed to initialize BackgroundSurveySyncService: $e');
      _cleanup();
      rethrow;
    }
  }

  /// Start background sync for survey data
  Future<void> startSurveySync() async {
    if (!_isInitialized) {
      await initialize();
    }

    if (_isSyncing) {
      log('Survey sync already in progress, skipping...');
      return;
    }

    try {
      _isSyncing = true;
      _statusController.add(SyncStatus.syncing);

      log('Starting background survey sync...');

      // Send sync command to isolate
      _syncSendPort?.send({
        'command': 'start_survey_sync',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });

    } catch (e) {
      log('Error starting survey sync: $e');
      _isSyncing = false;
      _statusController.add(SyncStatus.error);
    }
  }

  /// Handle messages from the sync isolate
  void _handleIsolateMessage(dynamic message) {
    try {
      if (message is Map<String, dynamic>) {
        final String type = message['type'] ?? '';

        switch (type) {
          case 'progress':
            final progress = SyncProgress.fromMap(message);
            _progressController.add(progress);
            break;

          case 'status':
            final status = SyncStatus.values.firstWhere(
              (s) => s.name == message['status'],
              orElse: () => SyncStatus.idle,
            );
            _statusController.add(status);

            if (status == SyncStatus.completed || status == SyncStatus.error) {
              _isSyncing = false;
            }
            break;

          case 'log':
            log('Isolate: ${message['message']}');
            break;

          case 'error':
            log('Isolate Error: ${message['error']}');
            _isSyncing = false;
            _statusController.add(SyncStatus.error);
            break;
        }
      }
    } catch (e) {
      log('Error handling isolate message: $e');
    }
  }

  /// Cleanup resources
  void _cleanup() {
    _syncIsolate?.kill(priority: Isolate.immediate);
    _syncIsolate = null;
    _syncReceivePort?.close();
    _syncReceivePort = null;
    _syncSendPort = null;
    _isInitialized = false;
    _isSyncing = false;
  }

  /// Dispose the service
  void dispose() {
    _cleanup();
    _progressController.close();
    _statusController.close();
  }
}

/// Entry point for the sync isolate
void _syncIsolateEntryPoint(SendPort mainSendPort) async {
  // Step 1: Create receive port for this isolate
  final isolateReceivePort = ReceivePort();

  // Step 2: Send the isolate's send port back to main isolate
  mainSendPort.send(isolateReceivePort.sendPort);

  // Step 3: Create sync worker
  final worker = _SyncWorker(mainSendPort);

  // Step 4: Listen for commands from main isolate
  await for (final message in isolateReceivePort) {
    if (message is Map<String, dynamic>) {
      final String command = message['command'] ?? '';

      switch (command) {
        case 'start_survey_sync':
          await worker.syncSurveyData();
          break;

        case 'start_image_sync':
          await worker.syncImages();
          break;

        case 'stop':
          isolateReceivePort.close();
          return;
      }
    }
  }
}
```

### Worker Implementation in Background Isolate

```dart
/// Worker class that handles the actual sync operations in the isolate
class _SyncWorker {
  final SendPort _mainSendPort;
  final SurveyService _surveyService = SurveyService();

  _SyncWorker(this._mainSendPort);

  /// Send message to main isolate
  void _sendMessage(Map<String, dynamic> message) {
    try {
      _mainSendPort.send(message);
    } catch (e) {
      // Ignore send errors in isolate to prevent crashes
    }
  }

  /// Send log message
  void _log(String message) {
    _sendMessage({
      'type': 'log',
      'message': message,
    });
  }

  /// Send progress update
  void _sendProgress(int current, int total, String operation) {
    _sendMessage({
      'type': 'progress',
      'current': current,
      'total': total,
      'operation': operation,
      'percentage': total > 0 ? (current / total * 100).round() : 0,
    });
  }

  /// Send status update
  void _sendStatus(SyncStatus status) {
    _sendMessage({
      'type': 'status',
      'status': status.name,
    });
  }

  /// Sync survey data in background
  Future<void> syncSurveyData() async {
    try {
      _sendStatus(SyncStatus.syncing);
      _log('Starting survey data sync...');

      // Check internet connectivity
      bool isOnline = await Utility.isConnected();
      if (!isOnline) {
        _log('No internet connection available for survey upload');
        _sendStatus(SyncStatus.error);
        return;
      }

      // Open survey box (this is a heavy operation)
      final box = await Hive.openBox('surveyBox');
      final totalRecords = box.keys.length;

      if (totalRecords == 0) {
        _log('No survey records to sync');
        _sendStatus(SyncStatus.completed);
        return;
      }

      _log('Found $totalRecords survey records to sync');
      _sendProgress(0, totalRecords, 'Syncing survey data');

      int uploadedCount = 0;
      int alreadyUploadedCount = 0;
      int skippedCount = 0;

      // Process each survey record (heavy operation that would block UI)
      for (int i = 0; i < box.keys.length; i++) {
        final key = box.keys.elementAt(i);

        try {
          final data = box.get(key);
          if (data == null) continue;

          final Map<String, dynamic> surveyMap = Map<String, dynamic>.from(data);

          // Check if already uploaded
          final bool isUploaded = surveyMap['isUploaded'] ?? false;
          if (isUploaded) {
            alreadyUploadedCount++;
            _sendProgress(i + 1, totalRecords, 'Syncing survey data');
            continue;
          }

          // Determine survey type and sync accordingly
          final String assetType = surveyMap['assetType'] ?? '';
          bool syncResult = false;

          switch (assetType) {
            case 'Pole':
              syncResult = await _syncPoleSurvey(surveyMap);
              break;
            case 'Switch Point':
              syncResult = await _syncSwitchPointSurvey(surveyMap);
              break;
            case 'Transformer':
              syncResult = await _syncTransformerSurvey(surveyMap);
              break;
            default:
              _log('Unknown asset type: $assetType');
              continue;
          }

          if (syncResult) {
            // Mark as uploaded
            surveyMap['isUploaded'] = true;
            await box.put(key, surveyMap);
            uploadedCount++;
            _log('Successfully synced $assetType survey');
          } else {
            _log('Failed to sync $assetType survey');
          }

          // Send progress update after each record
          _sendProgress(i + 1, totalRecords, 'Syncing survey data');

        } catch (e) {
          _log('Error processing survey record: $e');
          skippedCount++;
        }
      }

      _log('Survey sync completed. Uploaded: $uploadedCount, Already uploaded: $alreadyUploadedCount, Skipped: $skippedCount');
      _sendStatus(SyncStatus.completed);

    } catch (e) {
      _sendError('Survey sync failed: $e');
      _sendStatus(SyncStatus.error);
    }
  }
}
```

### Integration with UI Components

```dart
// File: lib/survey/background_sync_manager.dart

class BackgroundSyncManager {
  static BackgroundSyncManager? _instance;
  static BackgroundSyncManager get instance => _instance ??= BackgroundSyncManager._();

  // Background sync service
  final BackgroundSurveySyncService _syncService = BackgroundSurveySyncService.instance;

  // Periodic sync timer
  Timer? _periodicSyncTimer;

  /// Initialize the background sync manager
  Future<void> initialize() async {
    try {
      log('Initializing BackgroundSyncManager...');

      // Initialize the background sync service
      await _syncService.initialize();

      // Listen to sync status changes
      _syncService.statusStream.listen(_handleSyncStatusChange);

      // Start periodic sync (every 10 minutes)
      _startPeriodicSync();

      log('BackgroundSyncManager initialized successfully');

    } catch (e) {
      log('Failed to initialize BackgroundSyncManager: $e');
      rethrow;
    }
  }

  /// Start periodic background sync
  void _startPeriodicSync() {
    _periodicSyncTimer?.cancel();
    _periodicSyncTimer = Timer.periodic(const Duration(minutes: 10), (timer) {
      if (!_isSyncing) {
        startBackgroundSync();
      }
    });
    log('Periodic sync started (every 10 minutes)');
  }

  /// Start background sync for both survey data and images
  Future<void> startBackgroundSync() async {
    try {
      // Check internet connectivity first
      bool isOnline = await Utility.isConnected();
      if (!isOnline) {
        log('No internet connection available for background sync');
        return;
      }

      log('Starting background sync...');

      // Start survey data sync in background isolate
      await _syncService.startSurveySync();

      // Start image sync in background isolate
      await _syncService.startImageSync();

    } catch (e) {
      log('Error starting background sync: $e');
    }
  }
}
```

### UI Integration Example

```dart
// File: lib/survey/screens/base_survey.dart

class BaseSurvey extends ConsumerStatefulWidget {
  // Background sync manager
  final BackgroundSyncManager _syncManager = BackgroundSyncManager.instance;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Initialize background sync manager
      _initializeBackgroundSync();
    });
  }

  /// Initialize background sync manager
  Future<void> _initializeBackgroundSync() async {
    try {
      await _syncManager.initialize();
      log('Background sync manager initialized for base survey');
    } catch (e) {
      log('Failed to initialize background sync manager: $e');
    }
  }

  void _updateConnectionStatus(ConnectivityResult result) {
    final bool isConnected = result != ConnectivityResult.none;

    if (mounted) {
      setState(() {
        hasInternet = isConnected;
      });
    }

    if (isConnected && !_wasConnected) {
      log('Internet connection restored - triggering background sync');

      // Trigger background sync when internet is restored
      // This happens in background isolate - UI remains responsive
      _syncManager.startBackgroundSync();
    }

    _wasConnected = isConnected;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Survey'),
        actions: [
          // Background sync status indicator
          const CompactSyncStatusIndicator(),
          const SizedBox(width: 8),
        ],
      ),
      body: // ... rest of UI
    );
  }
}
```

This comprehensive implementation demonstrates how isolates provide true background processing for survey data synchronization, ensuring the UI remains responsive during bulk upload operations.
