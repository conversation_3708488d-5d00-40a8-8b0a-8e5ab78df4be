import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:schnell_pole_installation/survey/models/survey_image_model.dart';
import 'package:schnell_pole_installation/utils/image_compression_util.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

/// Utility class for migrating legacy base64 image data to optimized file-based storage
class ImageMigrationUtil {
  
  /// Check if there are any legacy base64 records that need migration
  static Future<bool> hasLegacyData() async {
    try {
      final box = await Hive.openBox('imagesBox');
      
      for (int i = 0; i < box.length; i++) {
        final imageMap = box.getAt(i) as Map<dynamic, dynamic>?;
        if (imageMap != null) {
          final model = MultiCapturedImageModel.fromMap(imageMap);
          if (model.hasLegacyData) {
            return true;
          }
        }
      }
      return false;
    } catch (e) {
      log('Error checking for legacy data: $e');
      return false;
    }
  }
  
  /// Get count of legacy records that need migration
  static Future<int> getLegacyRecordCount() async {
    try {
      final box = await Hive.openBox('imagesBox');
      int count = 0;
      
      for (int i = 0; i < box.length; i++) {
        final imageMap = box.getAt(i) as Map<dynamic, dynamic>?;
        if (imageMap != null) {
          final model = MultiCapturedImageModel.fromMap(imageMap);
          if (model.hasLegacyData) {
            count++;
          }
        }
      }
      return count;
    } catch (e) {
      log('Error counting legacy records: $e');
      return 0;
    }
  }
  
  /// Migrate a single base64 image to compressed file format
  static Future<String?> migrateBase64ToFile({
    required String base64Image,
    required String fileName,
  }) async {
    try {
      if (base64Image.isEmpty) return null;
      
      log('Migrating base64 image to file: $fileName');
      
      // Decode base64 to bytes
      final bytes = base64Decode(base64Image);
      
      // Get temporary directory to save the decoded image
      final tempDir = await getTemporaryDirectory();
      final tempImagePath = path.join(tempDir.path, 'temp_$fileName');
      
      // Write bytes to temporary file
      final tempFile = File(tempImagePath);
      await tempFile.writeAsBytes(bytes);
      
      // Compress and save using our compression utility
      final compressedPath = await ImageCompressionUtil.compressAndSaveImage(
        originalImagePath: tempImagePath,
        fileName: fileName,
      );
      
      // Clean up temporary file
      if (await tempFile.exists()) {
        await tempFile.delete();
      }
      
      log('Successfully migrated $fileName to: $compressedPath');
      return compressedPath;
      
    } catch (e) {
      log('Error migrating base64 image $fileName: $e');
      return null;
    }
  }
  
  /// Migrate all legacy base64 records to file-based format
  /// This is optional and can be run in background
  static Future<void> migrateAllLegacyData({
    Function(int current, int total)? onProgress,
  }) async {
    try {
      log('Starting migration of legacy base64 data...');
      
      final box = await Hive.openBox('imagesBox');
      final totalRecords = box.length;
      int processedCount = 0;
      int migratedCount = 0;
      
      for (int i = 0; i < totalRecords; i++) {
        try {
          final imageMap = box.getAt(i) as Map<dynamic, dynamic>?;
          if (imageMap == null) continue;
          
          final model = MultiCapturedImageModel.fromMap(imageMap);
          bool updated = false;
          
          // Migrate image 1 if needed
          if (model.base64Image1?.isNotEmpty == true && model.filePath1.isEmpty) {
            final filePath = await migrateBase64ToFile(
              base64Image: model.base64Image1!,
              fileName: model.fileName1.isNotEmpty ? model.fileName1 : 'migrated_image1_${DateTime.now().millisecondsSinceEpoch}.jpg',
            );
            if (filePath != null) {
              model.filePath1 = filePath;
              model.base64Image1 = null; // Clear base64 data to save space
              updated = true;
              migratedCount++;
            }
          }
          
          // Migrate image 2 if needed
          if (model.base64Image2?.isNotEmpty == true && model.filePath2.isEmpty) {
            final filePath = await migrateBase64ToFile(
              base64Image: model.base64Image2!,
              fileName: model.fileName2.isNotEmpty ? model.fileName2 : 'migrated_image2_${DateTime.now().millisecondsSinceEpoch}.jpg',
            );
            if (filePath != null) {
              model.filePath2 = filePath;
              model.base64Image2 = null; // Clear base64 data to save space
              updated = true;
              migratedCount++;
            }
          }
          
          // Migrate image 3 if needed
          if (model.base64Image3?.isNotEmpty == true && model.filePath3.isEmpty) {
            final filePath = await migrateBase64ToFile(
              base64Image: model.base64Image3!,
              fileName: model.fileName3.isNotEmpty ? model.fileName3 : 'migrated_image3_${DateTime.now().millisecondsSinceEpoch}.jpg',
            );
            if (filePath != null) {
              model.filePath3 = filePath;
              model.base64Image3 = null; // Clear base64 data to save space
              updated = true;
              migratedCount++;
            }
          }
          
          // Update the record if any migration occurred
          if (updated) {
            await box.putAt(i, model.toMap());
            log('Migrated record $i with ${migratedCount} images');
          }
          
          processedCount++;
          onProgress?.call(processedCount, totalRecords);
          
        } catch (e) {
          log('Error migrating record $i: $e');
          processedCount++;
          onProgress?.call(processedCount, totalRecords);
        }
      }
      
      log('Migration completed: $migratedCount images migrated from $processedCount records');
      
    } catch (e) {
      log('Error during migration: $e');
    }
  }
  
  /// Clean up any orphaned base64 data after successful migration
  static Future<void> cleanupMigratedData() async {
    try {
      final box = await Hive.openBox('imagesBox');
      int cleanedCount = 0;
      
      for (int i = 0; i < box.length; i++) {
        final imageMap = box.getAt(i) as Map<dynamic, dynamic>?;
        if (imageMap == null) continue;
        
        final model = MultiCapturedImageModel.fromMap(imageMap);
        bool updated = false;
        
        // Clear base64 data if we have corresponding file paths
        if (model.filePath1.isNotEmpty && model.base64Image1?.isNotEmpty == true) {
          model.base64Image1 = null;
          updated = true;
          cleanedCount++;
        }
        
        if (model.filePath2.isNotEmpty && model.base64Image2?.isNotEmpty == true) {
          model.base64Image2 = null;
          updated = true;
          cleanedCount++;
        }
        
        if (model.filePath3.isNotEmpty && model.base64Image3?.isNotEmpty == true) {
          model.base64Image3 = null;
          updated = true;
          cleanedCount++;
        }
        
        if (updated) {
          await box.putAt(i, model.toMap());
        }
      }
      
      log('Cleanup completed: $cleanedCount base64 entries removed');
      
    } catch (e) {
      log('Error during cleanup: $e');
    }
  }
}
