import 'package:flutter/material.dart';
import 'package:schnell_pole_installation/utils/constants.dart';

class CustomShape extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    Gradient gradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [darkBlue, darkBlue],
      tileMode: TileMode.clamp,
    );

    final Rect colorBounds = Rect.fromLTRB(0, 0, size.width, size.height);
    final Paint paint = Paint()..shader = gradient.createShader(colorBounds);
    Path path = Path();
    path.lineTo(size.width * 0.5, 0);
    path.lineTo(size.width * 0.8, size.height * 0.38);
    path.lineTo(size.width * 0.8, size.height);
    path.lineTo(size.width * 0.01, size.height);
    path.lineTo(0, size.height * 0.38);
    path.lineTo(size.width * 0.5, 0);
    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
