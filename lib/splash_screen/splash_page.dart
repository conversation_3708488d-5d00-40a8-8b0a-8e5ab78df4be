import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:receive_intent/receive_intent.dart' as rec;
import 'package:schnell_pole_installation/splash_screen/splash_controller.dart';
import 'package:schnell_pole_installation/utils/constants.dart';
import 'package:schnell_pole_installation/utils/loader.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

Future<void> fetchLatestVersion() async {
  // final apiKey = 'YOUR_42MATTERS_API_KEY';
  const packageName = 'com.schnelliot.polevault';

  const url =
      'https://play.google.com/store/apps/details?id=com.schnelliot.polevault';

  final response = await http.get(Uri.parse(url));

  if (response.statusCode == 200) {
    final data = json.decode(response.body);
    final appDetails = data['results'][packageName];
    if (appDetails != null) {
      var latestVersion = appDetails['latest_version'];
    }
  }
}

class _SplashPageState extends State<SplashPage> {
  final SplashController _service = SplashController();
  bool connectionStatus = false;
  int value = 0;
  rec.Intent? _initialIntent;
  bool checked = true;
  AppUpdateInfo? updateInfo;
  checkForUpdate() async {
    if (checked) {
      // Skip in-app update check in debug mode or when running from development
      if (kDebugMode) {
        debugPrint('Debug mode detected - skipping in-app update check');
        _init();
        return;
      }

      try {
        await InAppUpdate.checkForUpdate().then((info) {
          debugPrint('Update availability: ${info.updateAvailability}');
          debugPrint('The Updated Info is $info');
          if (info.updateAvailability == UpdateAvailability.updateAvailable) {
            Navigator.pushNamedAndRemoveUntil(
                context, updateRoute, (Route<dynamic> route) => false);
          } else {
            _init();
          }
        }).catchError((e) async {
          debugPrint('In-app update error: $e');

          // Check if it's the "app not owned" error (development mode)
          if (e.toString().contains('ERROR_APP_NOT_OWNED') ||
              e.toString().contains('TASK_FAILURE')) {
            debugPrint(
                'App not owned by Play Store - continuing with normal flow');
            _init();
            return;
          }

          // For other errors, show alert
          var res = await forceAlert(
            context,
            'assets/animation/userAlert.json',
          );
          if (res == '0') {
            _init();
          }
        });
      } catch (e) {
        debugPrint('Unexpected error in checkForUpdate: $e');
        _init(); // Continue with normal flow
      }
    }
  }

  @override
  void dispose() {
    // updateInfo;
    super.dispose();
    // updateInfo;
  }

  @override
  void initState() {
    checkForUpdate();
    // _init();
    super.initState();
  }

  Future<void> _init() async {
    try {
      final receivedIntent = await rec.ReceiveIntent.getInitialIntent();
      debugPrint('${receivedIntent!.extra}');
      if (receivedIntent.extra == null || receivedIntent.extra == 'null') {
        alertPopUpConstant(
            context,
            'Please login via Luminator app to proceed.',
            'assets/animation/userAlert.json');
        return;
      }
      setState(() async {
        _initialIntent = receivedIntent;
        region = receivedIntent.extra!['region'];
        token = receivedIntent.extra!['token'];
        zone = receivedIntent.extra!['zone'];
        ward = receivedIntent.extra!['ward'];
        wardId = receivedIntent.extra!['wardId'];
        userName = receivedIntent.extra!['userName'];
        customerId = receivedIntent.extra!['customerId'];
        userId = receivedIntent.extra!['userId'] ?? '';
        isLocationTrackingRequired =
            receivedIntent.extra!['isLocationTrackingRquired'] ?? false;
        refreshToken = receivedIntent.extra!['refreshToken'] ?? '';
        // poleNoTemplate = 'BBMP-SNL-<wardNo>-<poleNo>-<switchPointNo>';
        poleNoTemplate = receivedIntent.extra!['poleSchema'] ??
            ''; // for generating pole number
        var res = region;
        //
        if (res != null) {
          // alertPopUp(context,token,'assets/animation/userAlert.json');
          final wardNumberForPolePrefix = extractWardValue(ward);
          tokenCall(token, region, zone, ward, wardId, userName, customerId,
              poleNoTemplate, wardNumberForPolePrefix);

          final prefs = await SharedPreferences.getInstance();
          prefs.setString('token', token);
          prefs.setString('userId', userId);
          prefs.setString('refreshToken', refreshToken);
          prefs.setBool(
              'isLocationTrackingRquired', isLocationTrackingRequired);
        } else if (receivedIntent.extra!['start-paused'] == true) {
          alertPopUpConstant(
              context,
              'Please login via Luminator app to proceed.',
              'assets/animation/userAlert.json');
        } else {
          alertPopUpConstant(
              context,
              'Please login via Luminator app to proceed.',
              'assets/animation/userAlert.json');
        }
      });
    } catch (e) {
      debugPrint('The Error Is $e');
    }
  }

  String extractWardValue(String wardString) {
    final parts = wardString.split('-');
    return parts.isNotEmpty ? parts.last : '';
  }

  tokenCall(token, region, zone, ward, wardId, userName, customerId,
      poleNoTemplate, wardNumberForPolePrefix) {
    _service.loginCheck(context, token, region, zone, ward, wardId, userName,
        customerId, poleNoTemplate, wardNumberForPolePrefix);
  }

  @override
  Widget build(BuildContext context) {
    Tween<double> scaleTween = Tween<double>(begin: 0.05, end: 0.50);
    double height = MediaQuery.of(context).size.height;
    return Scaffold(
        body: SafeArea(
      child: Scaffold(
        body: Column(
          children: [
            SizedBox(
              height: height / 15,
            ),
            TweenAnimationBuilder(
              tween: scaleTween,
              duration: const Duration(milliseconds: 10),
              builder: (ctx, double scale, child) {
                return Transform.scale(scale: scale, child: child);
              },
              child: Image.asset(logoImage),
            ),
            SizedBox(
              height: height / 60,
            ),
            Text(appName, style: Theme.of(context).textTheme.headlineSmall),
            SizedBox(
              height: height / 10,
            ),
            spinkit,
            const Padding(
                padding: EdgeInsets.all(20),
                child: Text(
                  'Version: $appVersion',
                  style: TextStyle(
                      color: Color.fromARGB(248, 64, 124, 161),
                      fontWeight: FontWeight.bold,
                      fontSize: 14),
                ))
          ],
        ),
      ),
    ));
  }
}

var region;
var token;
var zone;
var ward;
var wardId;
var userName;
var customerId;
var userId;
var refreshToken;
var poleNoTemplate;
var isLocationTrackingRequired;
