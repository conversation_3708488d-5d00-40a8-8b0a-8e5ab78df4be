import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:camera/camera.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:schnell_pole_installation/DB/db_repo.dart';
import 'package:schnell_pole_installation/DB/map_model.dart';
import 'package:schnell_pole_installation/Provider/provider.dart';
import 'package:schnell_pole_installation/Pole_Installation_Page/pole_installation.dart';
import 'package:schnell_pole_installation/take_photo/image_address_append.dart';
import 'package:schnell_pole_installation/utils/constants.dart';
import 'package:schnell_pole_installation/utils/loader.dart';
import 'package:schnell_pole_installation/utils/utility.dart';

class TakePictureScreen extends StatefulWidget {
  const TakePictureScreen(
      {super.key,
      required this.camera,
      required this.poleNumber,
      required this.token});
  final CameraDescription camera;
  final String poleNumber;
  final String token;

  @override
  TakePictureScreenState createState() => TakePictureScreenState();
}

class TakePictureScreenState extends State<TakePictureScreen> {
  late CameraController _controller;
  late Timer timer;
  bool hasInternet = false;
  late Future<void> _initializeControllerFuture;
  List<String> imageUrl = [
    img1,
    img2,
    img3,
    img1,
  ];

  checkpermission() async {
    var camerastatus = await Permission.camera.status;
    if (camerastatus.isDenied) {
      Permission.camera.request();
    }
  }

  @override
  void initState() {
    super.initState();
    _controller = CameraController(widget.camera, ResolutionPreset.medium,
        enableAudio: false);
    _initializeControllerFuture = _controller.initialize();
    checkInternet(context);
    timer = Timer.periodic(const Duration(minutes: 5), (timer) {
      checkInternet(context);
    });
  }

  checkInternet(BuildContext context) {
    Utility.isConnected().then((value) {
      if (value) {
        setState(() {
          hasInternet = true;
        });
      } else {
        setState(() {
          hasInternet = false;
        });
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
          appBar: AppBar(
            centerTitle: true,
            leading: Container(),
            title: const Text(
              'Take a picture',
              style: TextStyle(color: Color.fromARGB(248, 32, 61, 78)),
            ),
            backgroundColor: const Color.fromARGB(248, 217, 232, 243),
          ),
          body: WillPopScope(
            onWillPop: () => Future.value(false),
            child: FutureBuilder<void>(
              future: _initializeControllerFuture,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.done) {
                  return SafeArea(
                    child: Column(
                      children: [
                        const SizedBox(
                          height: 10,
                        ),
                        SizedBox(
                          height: 80,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: List.generate(imageUrl.length, (index) {
                              return Image.asset(
                                imageUrl[index],
                                fit: BoxFit.cover,
                              );
                            }),
                          ),
                        ),
                        const SizedBox(
                          height: 20,
                        ),
                        SizedBox(
                            height: MediaQuery.of(context).size.height * 0.6,
                            child: CameraPreview(_controller)),
                      ],
                    ),
                  );
                } else {
                  return const Center(child: CircularProgressIndicator());
                }
              },
            ),
          ),
          floatingActionButton: SizedBox(
            height: 70.0,
            width: 70.0,
            child: FittedBox(
              child: FloatingActionButton(
                backgroundColor: const Color.fromARGB(248, 54, 89, 109),
                onPressed: () async {
                  loaderAnimation('loading...');
                  try {
                    await _initializeControllerFuture;
                    final image = await _controller.takePicture();
                    _controller.pausePreview();
                    final bytes = File(image.path).readAsBytesSync();
                    String img64 = base64Encode(bytes);
                    EasyLoading.dismiss();
                    _controller.pausePreview();
                    await Navigator.of(context)
                        .push(
                      MaterialPageRoute(
                        builder: (context) => DisplayPictureScreen(
                          imagePath: image.path,
                          hasInternet: hasInternet,
                        ),
                      ),
                    )
                        .then((value) {
                      _controller.resumePreview();
                    });
                  } catch (e) {
                    EasyLoading.dismiss();
                    Navigator.pushAndRemoveUntil(
                        context,
                        MaterialPageRoute(
                            builder: (context) => const PoleInstallation()),
                        (Route<dynamic> route) => false);
                    await snackBarLoader('Something Went Wrong', context);

                    debugPrint('$e');
                  }
                },
                child: const Icon(Icons.camera_alt),
              ),
            ),
          ),
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerFloat),
    );
  }
}

class DisplayPictureScreen extends StatelessWidget {
  final String imagePath;
  final bool hasInternet;

  const DisplayPictureScreen({
    super.key,
    required this.imagePath,
    required this.hasInternet,
  });

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    var height = size.height;
    var width = size.width;
    var dataBloc = Provider.of<DataModel>(context, listen: false);
    var dataBloc1 = Provider.of<PoleCountModel>(context, listen: false);
    ImageAddressAppender appender = ImageAddressAppender();
    return SafeArea(
      child: Scaffold(
        floatingActionButton: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              height: 60,
              child: FittedBox(
                child: FloatingActionButton(
                  heroTag: const Text("btn2"),
                  backgroundColor: const Color.fromARGB(248, 64, 124, 161),
                  onPressed: () async {
                    // Navigator.pushAndRemoveUntil(context, MaterialPageRoute(builder: (context)=>TakePictureScreen(camera: firstCamera)), (Route<dynamic> route) => false);
                    Navigator.of(context).pop(true);
                  },
                  child: const Icon(
                    Icons.restart_alt,
                    size: 26,
                  ),
                ),
              ),
            ),
            const SizedBox(
              width: 20,
            ),
            SizedBox(
              height: 60,
              child: FittedBox(
                  child: FloatingActionButton.extended(
                heroTag: const Text("btn1"),
                backgroundColor: Color.fromRGBO(64, 124, 161, 0.973),
                onPressed: () async {
                  final bytes = File(imagePath).readAsBytesSync();
                  String deviceImage = base64Encode(bytes);
                  var storeImage =
                      Provider.of<PoleCountModel>(context, listen: false);
                  storeImage.deviceImage = deviceImage;
                  storeImage.imagePoleName = dataBloc1.name ?? '';
                  storeImage.imageFileName = dataBloc1.uuidFileName ?? '';
                  storeImage.wtrmrkLatitude = dataBloc1.latitude;
                  storeImage.wtrmrkLongitude = dataBloc1.longitude;
                  storeImage.wtrmrkAccuracy = dataBloc1.accuracy;
                  storeImage.wtrmrkLocation = dataBloc1.location ?? '';
                  storeImage.wtrmrkManualEnteredLocation =
                      dataBloc1.manualEnteredLocation ?? '';
                  storeImage.wtrmrkWard = dataBloc1.wardName ?? '';
                  storeImage.wtrmrkZone = dataBloc1.zoneName ?? '';
                  storeImage.wtrmrkRegion = dataBloc1.region ?? '';
                  var localresult = await DatabaseRepo()
                      .insertImageData('tbl_image_db', storeImage);
                  dataBloc.poleCount = dataBloc.poleCount + 1;
                  await alertPopUpTextBold(
                      context,
                      'Pole ${dataBloc1.name} has been Successfully Installed',
                      'assets/animation/tick.json');
                  Navigator.pushAndRemoveUntil(
                      context,
                      MaterialPageRoute(
                          builder: (context) => const PoleInstallation()),
                      (Route<dynamic> route) => false);
                },
                icon: const Icon(Icons.check),
                label: const Text('Complete'),
              )),
            ),
          ],
        ),
        appBar: AppBar(
          leading: Container(),
          centerTitle: true,
          title: const Text(
            'Display the picture',
            style: TextStyle(color: Color.fromARGB(248, 32, 61, 78)),
          ),
          backgroundColor: const Color.fromARGB(248, 217, 232, 243),
        ),
        body: SingleChildScrollView(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(
                  height: 20,
                ),
                SizedBox(
                  height: height * 0.6,
                  child: Image.file(File(imagePath)),
                ),
                const SizedBox(
                  height: 10,
                ),
                Text(
                  'Pole No : ${dataBloc1.name}',
                  style: TextStyle(
                      color: bDarkBlue,
                      fontWeight: FontWeight.bold,
                      fontSize: 20),
                ),
                const SizedBox(
                  height: 10,
                ),
                dataBloc.hasInternet == false
                    ? Container(
                        height: 50,
                        width: width * 0.8,
                        decoration: BoxDecoration(
                            color: const Color(0xFF666666),
                            borderRadius: BorderRadius.circular(16)),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: const [
                            Icon(
                              Icons.cloud_off_outlined,
                              color: Colors.white,
                            ),
                            Text(
                              '  Offline!',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold),
                            ),
                          ],
                        ))
                    : Container(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
