import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:app_settings/app_settings.dart';
import 'package:barcode_scan2/barcode_scan2.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as r;
import 'package:geocoding/geocoding.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:schnell_pole_installation/DB/db_repo.dart';
import 'package:schnell_pole_installation/Provider/provider.dart';
import 'package:schnell_pole_installation/Pole_Details/pole_details.dart';
import 'package:schnell_pole_installation/Pole_Details/pole_details_controller.dart';
import 'package:schnell_pole_installation/Pole_Details/pole_display_details.dart';
import 'package:schnell_pole_installation/Pole_Installation_Page/pole_installation_controller.dart';
import 'package:schnell_pole_installation/splash_screen/login_service.dart';
import 'package:schnell_pole_installation/survey/background_sync_manager.dart';
import 'package:schnell_pole_installation/take_photo/take_photo_controller.dart';
import 'package:schnell_pole_installation/utils/box_container.dart';
import 'package:schnell_pole_installation/utils/constants.dart';
import 'package:schnell_pole_installation/utils/loader.dart';
import 'package:schnell_pole_installation/utils/utility.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../survey/screens/base_survey.dart';
import '../take_photo/image_address_append.dart';

class PoleInstallation extends r.ConsumerStatefulWidget {
  const PoleInstallation({
    super.key,
  });

  @override
  r.ConsumerState<PoleInstallation> createState() => _PoleInstallationState();
}

class _PoleInstallationState extends r.ConsumerState<PoleInstallation>
    with WidgetsBindingObserver {
  final PoleInstallationController _service = PoleInstallationController();
  final PoleDetailController detailService = PoleDetailController();
  final TakePhotoController imageService = TakePhotoController();
  List<ConnectivityResult> _connectionStatus = [ConnectivityResult.none];
  final Connectivity _connectivity = Connectivity();
  late StreamSubscription<List<ConnectivityResult>> _connectivitySubscription;
  final LoginService service = LoginService();
  int? poleCountNumber = 0;
  List historyTableData = [];
  String polecount = '';
  var displayDayData = {};
  int specificUserPoleCount = 0;
  var tableDates = {};
  bool hasInternet = false;
  bool isLoading = true;
  bool showAlert = true;
  // late Timer timer;
  late Timer sendDatatimer;
  late Timer sendImagetimer;
  var todaysDate =
      '${DateTime.now().day.toString().length == 1 ? '0${DateTime.now().day.toString()}' : DateTime.now().day.toString()}-${DateTime.now().month.toString().length == 1 ? '0${DateTime.now().month.toString()}' : DateTime.now().month.toString()}-${DateTime.now().year.toString()}';
  // var todaysDate = '30-07-2023';
  final BackgroundSyncManager _syncManager = BackgroundSyncManager.instance;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // ref.read(surveyController).fetchLocationForSurvey();
      // Check initial connectivity status
      _checkInitialConnectivity();
      // Initialize background sync manager
      _initializeBackgroundSync();
    });
    saveSharedPref();
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
    // initConnectivity();
    sendDatatimer = Timer.periodic(const Duration(minutes: 10), (value) {
      autoUploadData(context);
      // uploadSurveyData(context);
    });
    sendImagetimer = Timer.periodic(const Duration(minutes: 10), (value) {
      autoUploadImage(context);
      // uploadCapturedImages(context);
    });
  }

  saveSharedPref() async {
    var dataBloc = Provider.of<DataModel>(context, listen: false);
    SharedPreferences dataTime = await SharedPreferences.getInstance();
    var existingDate = dataTime.getString('date');
    var existingflag = dataTime.getString('flag');
    if (existingDate == null ||
        existingflag == null ||
        (todaysDate != existingDate) ||
        (todaysDate == existingDate && existingflag == '0')) {
      dataTime.setString('date', todaysDate);
      dataTime.setString('flag', '1');
      dataTime.setString('dayTableFlush', '0');
      dataTime.setString('wardId', dataBloc.wardId);
      await DatabaseRepo().deleteTableDetailsDatas('tbl_polecount_db');
      await DatabaseRepo().deleteTableDetailsDatas('tbl_date_db');
    }
  }

  /// Initialize background sync manager
  Future<void> _initializeBackgroundSync() async {
    try {
      await _syncManager.initialize();
      log('Background sync manager initialized for base survey');
    } catch (e) {
      log('Failed to initialize background sync manager: $e');
    }
  }

  /// Check initial connectivity status when screen loads
  Future<void> _checkInitialConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      await _updateConnectionStatus(result);
    } catch (e) {
      log('Error checking initial connectivity: $e');
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      // App came back to foreground, check connectivity
      log('App resumed - checking connectivity status');
      _checkConnectivityOnResume();
    }
  }

  /// Check connectivity when app resumes or screen comes back into focus
  Future<void> _checkConnectivityOnResume() async {
    try {
      final result = await _connectivity.checkConnectivity();
      await _updateConnectionStatus(result);
    } catch (e) {
      log('Error checking connectivity on resume: $e');
    }
  }

  Future<void> initConnectivity() async {
    late List<ConnectivityResult> result;
    // Platform messages may fail, so we use a try/catch PlatformException.
    try {
      result = await _connectivity.checkConnectivity();
      debugPrint('Connection Status : $result');
    } on PlatformException catch (e) {
      debugPrint(
        'Couldn\'t check connectivity status : $e',
      );
      return;
    }
    if (!mounted) {
      return Future.value(null);
    }
    return _updateConnectionStatus(result);
  }

  // Debounce mechanism for connectivity changes
  static DateTime? _lastConnectivityChange;
  static const Duration _connectivityDebounceDelay = Duration(seconds: 3);

  Future<void> _updateConnectionStatus(List<ConnectivityResult> result) async {
    var dataBloc = Provider.of<DataModel>(context, listen: false);
    setState(() {
      _connectionStatus = result;
    });
    debugPrint('Update Install Connection Status : $_connectionStatus');

    if (_connectionStatus.contains(ConnectivityResult.none) ||
        _connectionStatus.isEmpty) {
      setState(() {
        isLoading = false;

        hasInternet = false;
      });
      dataBloc.hasInternet = false;
      dbdetails();
      log('Internet connection lost');
    } else {
      // Debounce connectivity changes to prevent rapid upload attempts
      final now = DateTime.now();
      if (_lastConnectivityChange != null &&
          now.difference(_lastConnectivityChange!) <
              _connectivityDebounceDelay) {
        log('Connectivity change debounced - too soon since last change');
        setState(() {
          hasInternet = true;
        });
        dataBloc.hasInternet = true;
        return;
      }
      _lastConnectivityChange = now;

      log('Internet connection restored - triggering upload');

      // Trigger background sync when internet is restored
      // _syncManager.startBackgroundSync();
      await autoUploadData(context);
      await autoUploadImage(context);
      await poleCount(context);
      await dbdetails();

      setState(() {
        isLoading = true;
        hasInternet = true;
      });
      dataBloc.hasInternet = true;
    }
  }

  poleCount(BuildContext context) async {
    if (mounted) {
      var dataBloc = Provider.of<DataModel>(context, listen: false);
      Utility.isConnected().then((value) async {
        if (value) {
          var result =
              await service.poleCount(dataBloc.token, context, dataBloc.wardId);
          var dayData = await service.dayCount(
              dataBloc.token, context, dataBloc.wardId, dataBloc.userName);
          setState(() {
            polecount = result;
            displayDayData = dayData['User_history'];
            specificUserPoleCount = dayData['Total_count'];
            if (displayDayData['status'] == 401) {
              alertPopUpConstant(
                  context,
                  'Please login via Luminator app to proceed.',
                  'assets/animation/userAlert.json');
              return;
            }
            tableDates = jsonDecode(jsonEncode(displayDayData));
            Future.delayed(const Duration(seconds: 1));
            isLoading = false;
            hasInternet = true;
          });
          SharedPreferences tableCount = await SharedPreferences.getInstance();
          var res = tableCount.getString('dayTableFlush');
          var wardData = tableCount.getString('wardId');
          if (res == '0' || wardData != dataBloc.wardId) {
            var deletedata1 =
                await DatabaseRepo().deleteTableDetailsDatas('tbl_date_db');

            tableDates.forEach((key, value) async {
              var localresult = await DatabaseRepo().insertdayData(
                  'tbl_date_db',
                  {'date': key, 'polecount': value, 'wardId': dataBloc.wardId});
              // print(localresult);
            });

            // tableCount.setString('dayTableFlush', '1');
          }
          List localresult = await DatabaseRepo()
              .readWardDatabyWardIdlen('tbl_date_db', dataBloc.wardId);
          if (displayDayData[todaysDate] != localresult[0]['polecount']) {
            var localresult1 = await DatabaseRepo().increasePoleCountData(
                'tbl_date_db', displayDayData[todaysDate], todaysDate);
          }
        } else {
          // var result =
          //     await internetsnackBar(context, snacBarTitle, snackBarMessage);
          // if (result == '1') {
          //   poleCount();
          // }
          setState(() {
            isLoading = false;
          });
          // setState(() {
          //   polecount = '';
          // });
        }
      });
    }
  }

  dbdetails() async {
    var dataBloc = Provider.of<DataModel>(context, listen: false);
    List localresult = await DatabaseRepo()
        .readWardDatabyWardIdlen('tbl_polecount_db', dataBloc.wardId);
    List dateResult = await DatabaseRepo()
        .readWardDatabyWardId('tbl_date_db', dataBloc.wardId);
    setState(() {
      poleCountNumber = localresult.length;
      historyTableData = dateResult;
    });
    // setState(() {
    //   poleCountNumber.add(localresult);
    // });
    // print('//////////////////////////////////////////////');
    // log('$poleCountNumber');
  }

  autoUploadData(BuildContext context) async {
    Utility.isConnected().then((value) async {
      if (value) {
        List datalength =
            await DatabaseRepo().readAllDataDetails('tbl_pole_db');
        if (datalength.isNotEmpty) {
          for (int i = 1; i <= datalength.length; i++) {
            var localresult =
                await DatabaseRepo().readOneDetailsData('tbl_pole_db');
            // var imageResult =
            //     await DatabaseRepo().readOneDetailsData('tbl_image_db');
            await dataUpdation(localresult[0], context);
          }
        }
      }
    });
  }

  autoUploadImage(BuildContext context) async {
    Utility.isConnected().then((value) async {
      if (value) {
        List datalength =
            await DatabaseRepo().readAllDataDetails('tbl_image_db');
        if (datalength.isNotEmpty) {
          for (int i = 1; i <= datalength.length; i++) {
            var localresult =
                await DatabaseRepo().readOneDetailsData('tbl_image_db');
            await imageUpdation(localresult[0], context);
          }
        }
      }
    });
  }

  imageUpdation(imageData, BuildContext context) async {
    var dataBloc = Provider.of<DataModel>(context, listen: false);
    ImageAddressAppender appender = ImageAddressAppender();
    String imageBase64 = imageData['deviceImage'];
    var locationCheck = imageData['location'];
    String wtrmrkLocation = locationCheck == null || locationCheck == ''
        ? await getUserLocation(imageData['latitude'], imageData['longitude'])
        : locationCheck;
    Uint8List imageBytes = base64Decode(imageBase64);
    // Get the directory to save the image
    Directory tempDir = await getTemporaryDirectory();
    String tempPath = tempDir.path;
    File imageFile =
        File('$tempPath/image.png'); //Create a file to write the image bytes
    await imageFile
        .writeAsBytes(imageBytes); //Write the image bytes to the file

    // Step 5: Return the file path
    // return imageFile.path;
    await appender.appendAddressToImage(
      imageFile.path,
      imageData['latitude'],
      imageData['longitude'],
      imageData['name'] ?? '',
      imageData['accuracy'],
      imageData['manualEnteredLocation'] ?? '',
      wtrmrkLocation,
      imageData['region'] ?? '',
      imageData['zoneName'] ?? '',
      imageData['wardName'] ?? '',
    );
    final bytes = File(imageFile.path).readAsBytesSync();
    String deviceImage = base64Encode(bytes);
    var result = await imageService.imageUpdation(
        deviceImage, dataBloc.token, imageData, context);
  }

  dataUpdation(value, BuildContext context) async {
    var dataBloc = Provider.of<DataModel>(context, listen: false);
    var locationCheck = value['location'];
    var manualEnteredLocation = value['manualEnteredLocation'];
    String? filteredLandMark;
    if (locationCheck != null) {
      String? concatenatedLocation = '$manualEnteredLocation,$locationCheck';
      filteredLandMark = concatenatedLocation
          .replaceAll(RegExp(r',\s*'), ',')
          .replaceAll(RegExp(r'^,\s*'), '')
          .trim();
    }
    var clamp = value['clampDimension'];
    var clampJson = clamp == null ? null : json.decode(clamp);

    var lampProfileJson;
    if (value['lampProfiles'] != null) {
      var lampProfile = value['lampProfiles'];
      lampProfileJson = json.decode(lampProfile);
    }

    var dummyPole = value['armCount'];

    if (locationCheck == null) {
      String landMark =
          await getUserLocation(value['latitude'], value['longitude']);
      String? concatenatedLocation = '$manualEnteredLocation,$landMark';
      filteredLandMark = concatenatedLocation
          .replaceAll(RegExp(r',\s*'), ',')
          .replaceAll(RegExp(r'^,\s*'), '')
          .trim();

      var data = {
        "name": value['name'],
        "type": value['type'],
        "customerId": value['customerId'],
        "wardId": value['wardId'],
        "latitude": value['latitude'],
        "longitude": value['longitude'],
        "accuracy": value['accuracy'],
        "location": filteredLandMark,
        "state": value['state'],
        "wardName": value['wardName'],
        "zoneName": value['zoneName'],
        "region": value['region'],
        "installedOn": value['installedOn'],
        "installedBy": value['installedBy'],
        "connection": value['connection'],
        "armCount": value['armCount'],
        "auditImg": value['uuidFileName'],
      };
      if (clamp != null) {
        data["clampDimension"] = clampJson;
      }
      if (dummyPole != 0) {
        data["lampProfiles"] = lampProfileJson;
      }

      var result = await detailService.validatePollInstallSucced(
          context, data, dataBloc.token, value['name']);
      await poleCount(context);
    } else {
      var data = {
        "name": value['name'],
        "type": value['type'],
        "customerId": value['customerId'],
        "wardId": value['wardId'],
        "latitude": value['latitude'],
        "longitude": value['longitude'],
        "accuracy": value['accuracy'],
        "location": filteredLandMark,
        "state": value['state'],
        "wardName": value['wardName'],
        "zoneName": value['zoneName'],
        "region": value['region'],
        "installedOn": value['installedOn'],
        "installedBy": value['installedBy'],
        "connection": value['connection'],
        "armCount": value['armCount'],
        "auditImg": value['uuidFileName'],
      };
      if (clamp != null) {
        data["clampDimension"] = clampJson;
      }
      if (dummyPole != 0) {
        data["lampProfiles"] = lampProfileJson;
      }
      var result = await detailService.validatePollInstallSucced(
          context, data, dataBloc.token, value['name']);
      await poleCount(context);
    }
  }

  Future<String> getUserLocation(lat, long) async {
    List<Placemark> placemarks = await placemarkFromCoordinates(lat, long);
    Placemark place = placemarks[0];
    var locationData =
        '${place.subLocality}, ${place.locality}, ${place.administrativeArea}, ${place.country}, ${place.postalCode}';
    // location = '${place.name},${place.street},${place.subLocality}, ${place.locality},${place.postalCode},';
    return locationData;
  }

  @override
  void dispose() {
    _connectivitySubscription.cancel();
    // timer.cancel();
    sendDatatimer.cancel();
    sendImagetimer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    var width = size.width;
    var dataBloc = Provider.of<DataModel>(context, listen: false);

    return WillPopScope(
      onWillPop: () {
        return showExitPopup(context);
      },
      child: SafeArea(
        child: Scaffold(
            appBar: AppBar(
              leading: Container(),
              backgroundColor: lightBlue,
              elevation: 0.0,
              centerTitle: true,
              titleTextStyle: TextStyle(
                color: darkBlue,
                fontSize: 18.0,
              ),
              title: Text(
                'PoleVault',
                style: TextStyle(color: bDarkBlue),
              ),
            ),
            body: isLoading == false
                ? SafeArea(
                    child: Column(
                      children: [
                        const SizedBox(
                          height: 8,
                        ),
                        Flexible(
                          child: SizedBox(
                            height: 50,
                            child: ListView(
                              padding: EdgeInsets.zero,
                              scrollDirection: Axis.horizontal,
                              shrinkWrap: true,
                              physics: const ClampingScrollPhysics(),
                              children: [
                                const SizedBox(
                                  width: 10,
                                ),
                                Row(
                                  mainAxisAlignment: hasInternet == true
                                      ? MainAxisAlignment.spaceEvenly
                                      : MainAxisAlignment.center,
                                  children: [
                                    GestureDetector(
                                      child: BoxContainer.rectangleContainer(
                                          '${dataBloc.region} > ${dataBloc.zone} > ${dataBloc.ward}'),
                                      onTap: () {},
                                    ),
                                    const SizedBox(
                                      width: 10,
                                    ),
                                    hasInternet == true
                                        ? BoxContainer.numberContainer(
                                            polecount, Icons.business)
                                        : Container(),
                                    const SizedBox(
                                      width: 10,
                                    ),
                                    hasInternet == true
                                        ? BoxContainer.numberContainer(
                                            specificUserPoleCount,
                                            Icons.person_2_outlined)
                                        : Container(),
                                  ],
                                ),
                                hasInternet == true
                                    ? const SizedBox(
                                        width: 10,
                                      )
                                    : Container(),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(
                          height: 50,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            SizedBox(
                              width: width * 0.11,
                            ),
                            Text(
                              'Installation History (Last 5 Days)',
                              style: TextStyle(
                                  fontSize: 18,
                                  color: bDarkBlue,
                                  fontWeight: FontWeight.bold),
                            ),
                          ],
                        ),
                        const SizedBox(
                          height: 20,
                        ),

                        SizedBox(
                          width: width * 0.8,
                          child: Container(
                            decoration: BoxDecoration(
                              color: darkBlue,
                              border: Border.all(
                                color: darkBlue,
                                width: 1,
                              ),
                              borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(10),
                                  topRight: Radius.circular(10)),
                            ),
                            child: Table(
                              border: TableBorder.symmetric(
                                inside: BorderSide(
                                  width: 1,
                                  color: lightBlue,
                                ),
                              ),
                              children: const [
                                TableRow(children: [
                                  TableCell(
                                      child: Padding(
                                    padding: EdgeInsets.all(8.0),
                                    child: Text(
                                      "Date",
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold),
                                    ),
                                  )),
                                  TableCell(
                                      child: Padding(
                                    padding: EdgeInsets.all(8.0),
                                    child: Text(
                                      "Pole Count",
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold),
                                    ),
                                  )),
                                ]),
                              ],
                            ),
                          ),
                        ),
                        hasInternet == false && historyTableData.isEmpty
                            ? Container(
                                width: width * 0.8,
                                decoration: BoxDecoration(
                                    color: lightBlue,
                                    border: Border.all(
                                      color: Colors.grey.shade400,
                                      width: 0.5,
                                    ),
                                    borderRadius: historyTableData.isNotEmpty
                                        ? const BorderRadius.only(
                                            bottomLeft: Radius.circular(0),
                                            bottomRight: Radius.circular(0))
                                        : const BorderRadius.only(
                                            bottomLeft: Radius.circular(10),
                                            bottomRight: Radius.circular(10))),
                                child: Table(
                                  border: TableBorder.symmetric(
                                      inside: BorderSide(
                                    width: 1,
                                    color: Colors.grey.shade400,
                                  )), //table border
                                  children: [
                                    TableRow(children: [
                                      const TableCell(
                                          child: Padding(
                                        padding: EdgeInsets.all(10.0),
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Text(
                                              'Today',
                                              textAlign: TextAlign.center,
                                              style: TextStyle(
                                                color: Colors.black,
                                              ),
                                            )
                                          ],
                                        ),
                                      )),
                                      TableCell(
                                          child: Padding(
                                        padding: const EdgeInsets.all(10.0),
                                        child: Text(
                                          poleCountNumber.toString(),
                                          textAlign: TextAlign.center,
                                          style: const TextStyle(
                                            color: Colors.black,
                                          ),
                                        ),
                                      )),
                                    ]),
                                  ],
                                ),
                              )
                            : Container(),
                        hasInternet == false && historyTableData.isNotEmpty
                            ? Flexible(
                                flex: 2,
                                child: SizedBox(
                                  width: width * 0.8,
                                  child: ListView.builder(
                                      scrollDirection: Axis.vertical,
                                      shrinkWrap: true,
                                      padding: EdgeInsets.zero,
                                      itemCount: historyTableData.length,
                                      itemBuilder: (context, index) {
                                        var myObject = historyTableData[index];
                                        return Container(
                                          decoration: BoxDecoration(
                                              color: lightBlue,
                                              border: Border.all(
                                                color: Colors.grey.shade400,
                                                width: 0.5,
                                              ),
                                              borderRadius: index ==
                                                      historyTableData.length -
                                                          1
                                                  ? const BorderRadius.only(
                                                      bottomLeft:
                                                          Radius.circular(10),
                                                      bottomRight:
                                                          Radius.circular(10))
                                                  : const BorderRadius.only(
                                                      bottomLeft:
                                                          Radius.circular(0),
                                                      bottomRight:
                                                          Radius.circular(0))),
                                          child: Table(
                                            border: TableBorder.symmetric(
                                                inside: BorderSide(
                                              width: 1,
                                              color: Colors.grey.shade400,
                                            )), //table border
                                            children: [
                                              TableRow(children: [
                                                TableCell(
                                                    child: Padding(
                                                  padding: const EdgeInsets.all(
                                                      10.0),
                                                  child: Column(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .center,
                                                    children: [
                                                      Text(
                                                        myObject['date'] ==
                                                                todaysDate
                                                            ? 'Today'
                                                            : myObject['date'],
                                                        textAlign:
                                                            TextAlign.center,
                                                        style: const TextStyle(
                                                          color: Colors.black,
                                                        ),
                                                      )
                                                    ],
                                                  ),
                                                )),
                                                TableCell(
                                                    child: Padding(
                                                  padding: const EdgeInsets.all(
                                                      10.0),
                                                  child: Text(
                                                    myObject['polecount']
                                                        .toString(),
                                                    textAlign: TextAlign.center,
                                                    style: const TextStyle(
                                                      color: Colors.black,
                                                    ),
                                                  ),
                                                )),
                                              ]),
                                            ],
                                          ),
                                        );
                                      }),
                                ))
                            : Container(),
                        hasInternet == true && displayDayData['status'] != 401
                            ? Flexible(
                                flex: 2,
                                child: SizedBox(
                                  width: width * 0.8,
                                  child: ListView.builder(
                                      shrinkWrap: true,
                                      padding: EdgeInsets.zero,
                                      itemCount: displayDayData.length,
                                      itemBuilder: (context, index) {
                                        final entry = displayDayData.entries
                                            .toList()[index];
                                        final date = entry.key;
                                        final value = entry.value.toString();
                                        return Container(
                                          decoration: BoxDecoration(
                                              color: lightBlue,
                                              border: Border.all(
                                                color: Colors.grey.shade400,
                                                width: 0.5,
                                              ),
                                              borderRadius: index ==
                                                      displayDayData.length - 1
                                                  ? const BorderRadius.only(
                                                      bottomLeft:
                                                          Radius.circular(10),
                                                      bottomRight:
                                                          Radius.circular(10))
                                                  : const BorderRadius.only(
                                                      bottomLeft:
                                                          Radius.circular(0),
                                                      bottomRight:
                                                          Radius.circular(0))),
                                          child: Table(
                                            border: TableBorder.symmetric(
                                                inside: BorderSide(
                                              width: 1,
                                              color: Colors.grey.shade400,
                                            )), //table border
                                            children: [
                                              TableRow(children: [
                                                TableCell(
                                                    child: Padding(
                                                  padding: const EdgeInsets.all(
                                                      10.0),
                                                  child: Column(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .center,
                                                    children: [
                                                      Text(
                                                        todaysDate == date
                                                            ? 'Today'
                                                            : date,
                                                        textAlign:
                                                            TextAlign.center,
                                                        style: const TextStyle(
                                                          color: Colors.black,
                                                        ),
                                                      )
                                                    ],
                                                  ),
                                                )),
                                                TableCell(
                                                    child: Padding(
                                                  padding: const EdgeInsets.all(
                                                      10.0),
                                                  child: Text(
                                                    value,
                                                    textAlign: TextAlign.center,
                                                    style: const TextStyle(
                                                      color: Colors.black,
                                                    ),
                                                  ),
                                                )),
                                              ]),
                                            ],
                                          ),
                                        );
                                      }),
                                ))
                            : hasInternet == true
                                ? Center(
                                    child: Text(
                                      'Please Login Through Lumniator App!',
                                      style: TextStyle(color: bDarkBlue),
                                    ),
                                  )
                                : Container(),
                        //
                        const SizedBox(
                          height: 50,
                        ),

                        GestureDetector(
                          child: Container(
                            height: 50,
                            width: MediaQuery.of(context).size.width * 0.8,
                            decoration: BoxDecoration(
                                color: const Color.fromARGB(248, 64, 124, 161)
                                    .withOpacity(0.8),
                                borderRadius: BorderRadius.circular(50)),
                            child: const Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  'Pole Installation',
                                  style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16),
                                ),
                                SizedBox(
                                  width: 15,
                                ),
                                Icon(
                                  Icons.qr_code,
                                  color: Colors.white,
                                  size: 25,
                                ),
                              ],
                            ),
                          ),
                          onTap: () {
                            scanBarCode();
                          },
                        ),
                        const SizedBox(height: 10),

                        GestureDetector(
                          child: Container(
                            height: 50,
                            width: MediaQuery.of(context).size.width * 0.8,
                            decoration: BoxDecoration(
                                color: const Color.fromARGB(248, 64, 124, 161)
                                    .withOpacity(0.8),
                                borderRadius: BorderRadius.circular(50)),
                            child: const Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  'Survey',
                                  style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16),
                                ),
                                SizedBox(
                                  width: 15,
                                ),
                                Icon(
                                  Icons.notes_outlined,
                                  color: Colors.white,
                                  size: 25,
                                ),
                              ],
                            ),
                          ),
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      const BaseSurveyInformation()),
                            );
                          },
                        ),
                        const SizedBox(
                          height: 20,
                        ),
                        hasInternet == false
                            ? Container(
                                height: 50,
                                width: width * 0.8,
                                decoration: BoxDecoration(
                                    color: const Color(0xFF666666),
                                    borderRadius: BorderRadius.circular(16)),
                                child: const Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.cloud_off_outlined,
                                      color: Colors.white,
                                    ),
                                    Text(
                                      '  Offline!',
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold),
                                    ),
                                  ],
                                ))
                            : Container(),
                      ],
                    ),
                  )
                : Center(
                    child: CircularProgressIndicator(
                      color: darkBlue,
                    ),
                  )),
      ),
    );
  }

  scanBarCode() async {
    final dataBloc = Provider.of<DataModel>(context, listen: false);
    PermissionStatus camera = await Permission.camera.status;
    if (camera == PermissionStatus.granted) {
      final result = await BarcodeScanner.scan(
          options: const ScanOptions(strings: {
        'cancel': 'CANCEL',
        'flash_on': 'Flash on',
        'flash_off': 'Flash off'
      }));
      if (result.rawContent != '') {
        loaderAnimation('loading...');
        String trimmedContent = result.rawContent.trim();

        if (trimmedContent.contains(RegExp(r'^[0-9]+$')) != true &&
            trimmedContent.contains(RegExp(r'^[a-zA-Z]+$')) != true &&
            trimmedContent.contains(RegExp(r'[!@#$%^&*()-,.?":{}|<>]')) !=
                true &&
            (trimmedContent.length == 7 || trimmedContent.length == 8) &&
            trimmedContent.contains(RegExp(r'\s')) != true) {
          if (trimmedContent.length == 7 || trimmedContent.length == 8) {
            if (trimmedContent[0].contains(RegExp(r'^[a-zA-Z]+$'))) {
              EasyLoading.dismiss(animation: true);
              Utility.isConnected().then((value) async {
                if (value) {
                  List localData = await DatabaseRepo()
                      .readDetailsDatabyPoleName('tbl_pole_db', trimmedContent);
                  //if the pole already not installed
                  if (localData.isEmpty) {
                    var data = _service.validatePoll(
                        trimmedContent,
                        context,
                        dataBloc.token,
                        '0',
                        polecount,
                        specificUserPoleCount,
                        dataBloc.region,
                        dataBloc.zone,
                        dataBloc.ward);
                  } else {
                    // if it is already installed pole
                    List dataImage = await DatabaseRepo()
                        .readDetailsDatabyPoleName(
                            'tbl_image_db', trimmedContent);
                    // ignore: use_build_context_synchronously
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => PoleDisplayDetails(
                                  token: dataBloc.token,
                                  region: localData[0]['region'],
                                  zone: localData[0]['zoneName'],
                                  ward: localData[0]['wardName'],
                                  poleNumber: localData[0]['name'],
                                  poleType: localData[0]['type'],
                                  noOfArms: localData[0]['armCount'].toString(),
                                  clampDimension:
                                      localData[0]['clampDimension'] == null
                                          ? ''
                                          : localData[0]['clampDimension'],
                                  lampProfiles:
                                      localData[0]['lampProfiles'] ?? '',
                                  // lampType: localData[0]['lampType'],
                                  // lampWattage: localData[0]['lampWattage'],
                                  customerId: localData[0]['customerId'],
                                  wardId: localData[0]['wardId'],
                                  latitude: localData[0]['latitude'].toString(),
                                  longitude:
                                      localData[0]['longitude'].toString(),
                                  accuracy: localData[0]['accuracy'].toString(),
                                  location: localData[0]['location'] == null
                                      ? ''
                                      : localData[0]['location'],
                                  state: localData[0]['state'],
                                  installedOn:
                                      localData[0]['installedOn'].toString(),
                                  installedBy: localData[0]['installedBy'],
                                  auditPicS3Url: localData.isNotEmpty
                                      ? dataImage[0]['deviceImage']
                                      : '',
                                  connection: localData[0]['connection'],
                                  poleCount: '',
                                  specificUserPoleCount: 0,
                                  defaultregion: dataBloc.region,
                                  defaultzone: dataBloc.zone,
                                  defaultward: dataBloc.ward,
                                )));
                  }
                } else {
                  List data = await DatabaseRepo()
                      .readDetailsDatabyPoleName('tbl_pole_db', trimmedContent);
                  if (data.isEmpty) {
                    var dateTime =
                        DateTime.now().millisecondsSinceEpoch.toString();
                    // ignore: use_build_context_synchronously
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => PoleDetails(
                                  token: dataBloc.token,
                                  region: dataBloc.region,
                                  zone: dataBloc.zone,
                                  ward: dataBloc.ward,
                                  poleNumber: trimmedContent,
                                  customerId: dataBloc.customerId,
                                  wardId: dataBloc.wardId,
                                  installedOn: dateTime,
                                  installedBy: dataBloc.userName,
                                  poleCount: polecount,
                                  specificUserPoleCount: specificUserPoleCount,
                                )));
                  } else {
                    List dataImage = await DatabaseRepo()
                        .readDetailsDatabyPoleName(
                            'tbl_image_db', trimmedContent);
                    // ignore: use_build_context_synchronously
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => PoleDisplayDetails(
                                  token: dataBloc.token,
                                  region: data[0]['region'],
                                  zone: data[0]['zoneName'],
                                  ward: data[0]['wardName'],
                                  poleNumber: data[0]['name'],
                                  poleType: data[0]['type'],
                                  noOfArms: data[0]['armCount'].toString(),
                                  clampDimension:
                                      data[0]['clampDimension'] == null
                                          ? ''
                                          : data[0]['clampDimension'],
                                  lampProfiles: data[0]['lampProfiles'] ?? '',
                                  customerId: data[0]['customerId'],
                                  wardId: data[0]['wardId'],
                                  latitude: data[0]['latitude'].toString(),
                                  longitude: data[0]['longitude'].toString(),
                                  accuracy: data[0]['accuracy'].toString(),
                                  location: data[0]['location'] == null
                                      ? ''
                                      : data[0]['location'],
                                  state: data[0]['state'],
                                  installedOn:
                                      data[0]['installedOn'].toString(),
                                  installedBy: data[0]['installedBy'],
                                  auditPicS3Url: dataImage.isNotEmpty
                                      ? dataImage[0]['deviceImage']
                                      : '',
                                  connection: data[0]['connection'],
                                  poleCount: '',
                                  specificUserPoleCount: 0,
                                  defaultregion: dataBloc.region,
                                  defaultzone: dataBloc.zone,
                                  defaultward: dataBloc.ward,
                                )));
                  }
                }
              });
            } else {
              EasyLoading.dismiss(animation: true);
              alertPopUp1(
                  context,
                  'Pole number format is Invalid. Kindly scan a different QR code.',
                  'assets/animation/warn.json');
            }
          } else {
            EasyLoading.dismiss(animation: true);
            alertPopUp1(
                context,
                'Pole number format is Invalid. Kindly scan a different QR code.',
                'assets/animation/warn.json');
          }
        } else if (trimmedContent.contains(RegExp(r'^[0-9]+$')) == true &&
            trimmedContent.contains(RegExp(r'^[a-zA-Z]+$')) == true) {
          EasyLoading.dismiss(animation: true);
          alertPopUp1(
              context,
              'Pole number format is Invalid. Kindly scan a different QR code.',
              'assets/animation/warn.json');
        } else if (trimmedContent.length != 7 || trimmedContent.length != 8) {
          EasyLoading.dismiss(animation: true);
          alertPopUp1(
              context,
              'Pole number format is Invalid. Kindly scan a different QR code.',
              'assets/animation/warn.json');
        } else {
          EasyLoading.dismiss(animation: true);
        }
      }
    } else {
      await Permission.camera.request();
      PermissionStatus camera = await Permission.camera.status;
      if (camera == PermissionStatus.granted) {
        scanBarCode();
      } else if (camera == PermissionStatus.permanentlyDenied) {
        AppSettings.openAppSettings();
        scanBarCode();
      }
    }
  }
}
