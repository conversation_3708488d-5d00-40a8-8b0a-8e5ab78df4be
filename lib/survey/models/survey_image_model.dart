class MultiCapturedImageModel {
  String filePath1;
  String fileName1;
  bool isUploaded1;

  // Legacy base64 fields for backward compatibility
  String? base64Image1;

  String filePath2;
  String fileName2;
  bool isUploaded2;

  // Legacy base64 fields for backward compatibility
  String? base64Image2;

  String filePath3;
  String fileName3;
  bool isUploaded3;

  // Legacy base64 fields for backward compatibility
  String? base64Image3;

  bool get isAllUploaded => isUploaded1 && isUploaded2 && isUploaded3;

  MultiCapturedImageModel({
    required this.filePath1,
    required this.fileName1,
    this.isUploaded1 = false,
    this.base64Image1,
    required this.filePath2,
    required this.fileName2,
    this.isUploaded2 = false,
    this.base64Image2,
    required this.filePath3,
    required this.fileName3,
    this.isUploaded3 = false,
    this.base64Image3,
  });

  Map<String, dynamic> toMap() {
    return {
      'filePath1': filePath1,
      'fileName1': fileName1,
      'isUploaded1': isUploaded1,
      'base64Image1': base64Image1, // Keep for backward compatibility
      'filePath2': filePath2,
      'fileName2': fileName2,
      'isUploaded2': isUploaded2,
      'base64Image2': base64Image2, // Keep for backward compatibility
      'filePath3': filePath3,
      'fileName3': fileName3,
      'isUploaded3': isUploaded3,
      'base64Image3': base64Image3, // Keep for backward compatibility
    };
  }

  factory MultiCapturedImageModel.fromMap(Map<dynamic, dynamic> map) {
    return MultiCapturedImageModel(
      filePath1: map['filePath1'] ?? '',
      fileName1: map['fileName1'] ?? '',
      isUploaded1: map['isUploaded1'] ?? false,
      base64Image1: map['base64Image1'], // Load legacy base64 data
      filePath2: map['filePath2'] ?? '',
      fileName2: map['fileName2'] ?? '',
      isUploaded2: map['isUploaded2'] ?? false,
      base64Image2: map['base64Image2'], // Load legacy base64 data
      filePath3: map['filePath3'] ?? '',
      fileName3: map['fileName3'] ?? '',
      isUploaded3: map['isUploaded3'] ?? false,
      base64Image3: map['base64Image3'], // Load legacy base64 data
    );
  }

  /// Check if this record has legacy base64 data (from old app version)
  bool get hasLegacyData =>
      (base64Image1?.isNotEmpty == true && filePath1.isEmpty) ||
      (base64Image2?.isNotEmpty == true && filePath2.isEmpty) ||
      (base64Image3?.isNotEmpty == true && filePath3.isEmpty);

  /// Get image data for upload - prioritizes file path over base64
  String? getImageData1() {
    if (filePath1.isNotEmpty) return null; // Will use file path
    return base64Image1; // Fallback to base64 for legacy data
  }

  String? getImageData2() {
    if (filePath2.isNotEmpty) return null; // Will use file path
    return base64Image2; // Fallback to base64 for legacy data
  }

  String? getImageData3() {
    if (filePath3.isNotEmpty) return null; // Will use file path
    return base64Image3; // Fallback to base64 for legacy data
  }
}
