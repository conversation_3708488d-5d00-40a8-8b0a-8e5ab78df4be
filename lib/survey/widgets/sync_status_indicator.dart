import 'dart:async';
import 'package:flutter/material.dart';
import 'package:schnell_pole_installation/survey/background_sync_manager.dart';
import 'package:schnell_pole_installation/survey/connectivity_sync_service.dart';

/// A widget that displays the current sync status and progress
/// Shows sync progress without blocking the main UI thread
class SyncStatusIndicator extends StatefulWidget {
  final bool showProgress;
  final bool showText;
  final double iconSize;
  final EdgeInsets padding;

  const SyncStatusIndicator({
    super.key,
    this.showProgress = true,
    this.showText = true,
    this.iconSize = 24.0,
    this.padding = const EdgeInsets.all(8.0),
  });

  @override
  State<SyncStatusIndicator> createState() => _SyncStatusIndicatorState();
}

class _SyncStatusIndicatorState extends State<SyncStatusIndicator>
    with TickerProviderStateMixin {
  final BackgroundSyncManager _syncManager = BackgroundSyncManager.instance;

  StreamSubscription<SyncStatus>? _statusSubscription;
  StreamSubscription<SyncProgress>? _progressSubscription;

  SyncStatus _currentStatus = SyncStatus.idle;
  SyncProgress? _currentProgress;
  SyncStatusInfo? _statusInfo;

  late AnimationController _rotationController;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize rotation animation for sync indicator
    _rotationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_rotationController);

    // Listen to sync status and progress
    _initializeListeners();

    // Get initial status
    _updateStatusInfo();
  }

  void _initializeListeners() {
    _statusSubscription = _syncManager.statusStream.listen((status) {
      if (mounted) {
        setState(() {
          _currentStatus = status;
          _updateStatusInfo();
        });

        // Start/stop rotation animation based on sync status
        if (status == SyncStatus.syncing) {
          _rotationController.repeat();
        } else {
          _rotationController.stop();
        }
      }
    });

    _progressSubscription = _syncManager.progressStream.listen((progress) {
      if (mounted) {
        setState(() {
          _currentProgress = progress;
        });
      }
    });
  }

  void _updateStatusInfo() {
    _statusInfo = _syncManager.syncStatusInfo;
  }

  @override
  void dispose() {
    _statusSubscription?.cancel();
    _progressSubscription?.cancel();
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: widget.padding,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildSyncIcon(),
          if (widget.showText || widget.showProgress) ...[
            const SizedBox(width: 8),
            _buildSyncInfo(),
          ],
        ],
      ),
    );
  }

  Widget _buildSyncIcon() {
    final statusInfo = _statusInfo ?? _syncManager.syncStatusInfo;

    Widget icon;
    if (_currentStatus == SyncStatus.syncing) {
      icon = AnimatedBuilder(
        animation: _rotationAnimation,
        builder: (context, child) {
          return Transform.rotate(
            angle: _rotationAnimation.value * 2 * 3.14159,
            child: Icon(
              Icons.sync,
              size: widget.iconSize,
              color: statusInfo.statusColor,
            ),
          );
        },
      );
    } else {
      icon = Icon(
        statusInfo.statusIcon,
        size: widget.iconSize,
        color: statusInfo.statusColor,
      );
    }

    return icon;
  }

  Widget _buildSyncInfo() {
    final statusInfo = _statusInfo ?? _syncManager.syncStatusInfo;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.showText)
          Text(
            statusInfo.statusText,
            style: TextStyle(
              fontSize: 12,
              color: statusInfo.statusColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        if (widget.showProgress && _currentProgress != null) ...[
          const SizedBox(height: 2),
          _buildProgressIndicator(),
        ],
      ],
    );
  }

  Widget _buildProgressIndicator() {
    if (_currentProgress == null) return const SizedBox.shrink();

    final progress = _currentProgress!;
    final percentage = progress.percentage / 100.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: 120,
          height: 4,
          child: LinearProgressIndicator(
            value: percentage,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              _statusInfo?.statusColor ?? Colors.blue,
            ),
          ),
        ),
        const SizedBox(height: 2),
        Text(
          '${progress.operation} (${progress.current}/${progress.total})',
          style: const TextStyle(
            fontSize: 10,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }
}

/// A compact sync status indicator for app bars
class CompactSyncStatusIndicator extends StatelessWidget {
  final double size;

  const CompactSyncStatusIndicator({
    super.key,
    this.size = 20.0,
  });

  @override
  Widget build(BuildContext context) {
    return SyncStatusIndicator(
      showProgress: false,
      showText: false,
      iconSize: size,
      padding: EdgeInsets.zero,
    );
  }
}

/// A detailed sync status card for settings or status pages
class DetailedSyncStatusCard extends StatelessWidget {
  final VoidCallback? onManualSync;

  const DetailedSyncStatusCard({
    super.key,
    this.onManualSync,
  });

  @override
  Widget build(BuildContext context) {
    final syncManager = BackgroundSyncManager.instance;

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.cloud_sync, size: 24),
                const SizedBox(width: 8),
                const Spacer(),
                if (onManualSync != null)
                  IconButton(
                    onPressed: syncManager.isSyncing ? null : onManualSync,
                    icon: const Icon(Icons.refresh),
                    tooltip: 'Manual Sync',
                  ),
              ],
            ),
            const SizedBox(height: 16),
            const SyncStatusIndicator(
              showProgress: true,
              showText: true,
              iconSize: 20,
              padding: EdgeInsets.zero,
            ),
          ],
        ),
      ),
    );
  }
}

/// Upload progress indicator for app bar - shows icon with count
class UploadProgressIndicator extends StatefulWidget {
  final double iconSize;

  const UploadProgressIndicator({
    super.key,
    this.iconSize = 20.0,
  });

  @override
  State<UploadProgressIndicator> createState() =>
      _UploadProgressIndicatorState();
}

class _UploadProgressIndicatorState extends State<UploadProgressIndicator>
    with TickerProviderStateMixin {
  final BackgroundSyncManager _syncManager = BackgroundSyncManager.instance;

  StreamSubscription<SyncStatus>? _statusSubscription;
  StreamSubscription<SyncProgress>? _progressSubscription;

  SyncStatus _currentStatus = SyncStatus.idle;
  SyncProgress? _currentProgress;
  SyncStatusInfo? _statusInfo;

  late AnimationController _rotationController;
  late Animation<double> _rotationAnimation;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize rotation animation for sync indicator
    _rotationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_rotationController);

    // Initialize pulse animation for upload indicator
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Listen to sync status and progress
    _initializeListeners();

    // Get initial status
    _updateStatusInfo();
  }

  void _initializeListeners() {
    _statusSubscription = _syncManager.statusStream.listen((status) {
      if (mounted) {
        setState(() {
          _currentStatus = status;
          _updateStatusInfo();
        });

        // Start/stop animations based on sync status
        if (status == SyncStatus.syncing) {
          _rotationController.repeat();
          _pulseController.repeat(reverse: true);
        } else {
          _rotationController.stop();
          _pulseController.stop();
        }
      }
    });

    _progressSubscription = _syncManager.progressStream.listen((progress) {
      if (mounted) {
        setState(() {
          _currentProgress = progress;
        });
      }
    });
  }

  void _updateStatusInfo() {
    _statusInfo = _syncManager.syncStatusInfo;
  }

  @override
  void dispose() {
    _statusSubscription?.cancel();
    _progressSubscription?.cancel();
    _rotationController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final statusInfo = _statusInfo ?? _syncManager.syncStatusInfo;
    final progress = _currentProgress;

    // Don't show anything if idle and no progress
    if (_currentStatus == SyncStatus.idle && progress == null) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _currentStatus == SyncStatus.syncing
            ? Colors.blue.withOpacity(0.1)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildUploadIcon(statusInfo),
          if (progress != null && _currentStatus == SyncStatus.syncing) ...[
            const SizedBox(width: 4),
            _buildProgressCount(progress),
          ],
        ],
      ),
    );
  }

  Widget _buildUploadIcon(SyncStatusInfo statusInfo) {
    Widget icon;

    if (_currentStatus == SyncStatus.syncing) {
      // Show animated upload icon during sync
      icon = AnimatedBuilder(
        animation: _pulseAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _pulseAnimation.value,
            child: AnimatedBuilder(
              animation: _rotationAnimation,
              builder: (context, child) {
                return Transform.rotate(
                  angle: _rotationAnimation.value * 2 * 3.14159,
                  child: Icon(
                    Icons.cloud_upload,
                    size: widget.iconSize,
                    color: Colors.blue,
                  ),
                );
              },
            ),
          );
        },
      );
    } else if (_currentStatus == SyncStatus.completed) {
      // Show success icon briefly
      icon = Icon(
        Icons.cloud_done,
        size: widget.iconSize,
        color: Colors.green,
      );
    } else if (_currentStatus == SyncStatus.error) {
      // Show error icon
      icon = Icon(
        Icons.cloud_off,
        size: widget.iconSize,
        color: Colors.red,
      );
    } else {
      // Default sync icon
      icon = Icon(
        statusInfo.statusIcon,
        size: widget.iconSize,
        color: statusInfo.statusColor,
      );
    }

    return icon;
  }

  Widget _buildProgressCount(SyncProgress progress) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.blue,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Text(
        '${progress.current}/${progress.total}',
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
