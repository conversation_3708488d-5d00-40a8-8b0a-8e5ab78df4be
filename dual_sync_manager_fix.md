# Dual Sync Manager Fix

## Problem Identified
There are **TWO separate `_isSyncing` flags** that were not properly coordinated:

1. **`ConnectivitySyncService._isSyncing`** - Used by `triggerSyncAfterDataInsertion()` (immediate sync after survey creation)
2. **`BackgroundSyncManager._isSyncing`** - Used by `startBackgroundSync()` (UI-triggered sync)

## Root Cause
When you do the offline→online scenario:
1. **Offline survey created** → Saved with `isUploaded = false`
2. **Go online** → Connectivity change triggers sync via `BackgroundSyncManager`
3. **Sync completes** → `BackgroundSyncManager._isSyncing = false` ✅
4. **Create online survey** → Calls `triggerSyncAfterDataInsertion()` 
5. **Immediate sync starts** → `ConnectivitySyncService._isSyncing = true`
6. **Sync gets stuck** → `ConnectivitySyncService._isSyncing` never resets to `false` ❌
7. **Next sync attempt** → `ConnectivitySyncService._performBackgroundSync()` sees `_isSyncing = true` and skips

## Solution Applied

### 1. Added Timeout Detection to ConnectivitySyncService
```dart
if (_isSyncing) {
  log('ConnectivitySyncService: Sync already in progress, skipping...');
  
  // Check if sync has been stuck for too long (more than 2 minutes)
  if (_syncStartTime != null && 
      DateTime.now().difference(_syncStartTime!).inMinutes > 2) {
    log('ConnectivitySyncService: Sync appears to be stuck, forcing reset...');
    _isSyncing = false;
    _syncStartTime = null;
    _statusController.add(SyncStatus.error);
  } else {
    return;
  }
}
```

### 2. Added Sync Start Time Tracking
```dart
try {
  _isSyncing = true;
  _syncStartTime = DateTime.now();
  _statusController.add(SyncStatus.syncing);
  log('ConnectivitySyncService: Starting sync at $_syncStartTime...');
}
```

### 3. Clear Start Time on Completion/Error
```dart
case 'sync_complete':
  _isSyncing = false;
  _syncStartTime = null;  // ← Added this
  // ...

case 'sync_error':
  _isSyncing = false;
  _syncStartTime = null;  // ← Added this
  // ...
```

### 4. Added Debug Methods
```dart
// For ConnectivitySyncService
void logCurrentStatus() {
  log('ConnectivitySyncService Status:');
  log('  _isSyncing: $_isSyncing');
  log('  _isolateInitialized: $_isolateInitialized');
  log('  _syncStartTime: $_syncStartTime');
}

void forceResetSyncStatus() {
  _isSyncing = false;
  _syncStartTime = null;
  _statusController.add(SyncStatus.idle);
}
```

## Expected Behavior After Fix

### Scenario: Offline → Online → Online Survey
1. **Offline survey created** → `isUploaded = false`
2. **Go online** → Background sync uploads offline survey
3. **Background sync completes** → Both `_isSyncing` flags reset to `false`
4. **Create online survey** → `triggerSyncAfterDataInsertion()` called
5. **Immediate sync starts** → `ConnectivitySyncService._isSyncing = true`
6. **Immediate sync completes** → `ConnectivitySyncService._isSyncing = false`
7. **Next sync works** → No "already in progress" message

### Auto-Recovery
- If `ConnectivitySyncService._isSyncing` gets stuck for >2 minutes → Auto-reset
- If `BackgroundSyncManager._isSyncing` gets stuck for >2 minutes → Auto-reset

## Testing Steps

1. **Test the problematic scenario**:
   - Install app, go offline, create survey
   - Go online, wait for sync to complete
   - Create another survey online
   - Should NOT see "background processing is going on skipping"

2. **Check debug logs**:
   ```dart
   ConnectivitySyncService.instance.logCurrentStatus();
   BackgroundSyncManager.instance.logCurrentStatus();
   ```

3. **Manual recovery** (if needed):
   ```dart
   ConnectivitySyncService.instance.forceResetSyncStatus();
   BackgroundSyncManager.instance.forceResetSyncStatus();
   ```

## Key Log Messages to Watch

### Success Flow:
- `"ConnectivitySyncService: Starting sync at [timestamp]"`
- `"Background sync completed in isolate"`
- `"BackgroundSyncManager: Setting _isSyncing = false (completed)"`

### Auto-Recovery:
- `"ConnectivitySyncService: Sync appears to be stuck, forcing reset..."`
- `"BackgroundSyncManager: Sync appears to be stuck, forcing reset..."`

The fix ensures both sync managers properly coordinate and auto-recover from stuck states.
