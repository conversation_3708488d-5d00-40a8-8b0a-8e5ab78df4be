import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:schnell_pole_installation/survey/background_sync_manager.dart';
import 'package:schnell_pole_installation/survey/background_survey_sync_service.dart';

void main() {
  group('Background Sync Tests', () {
    late BackgroundSyncManager syncManager;
    late BackgroundSurveySyncService syncService;

    setUp(() {
      syncManager = BackgroundSyncManager.instance;
      syncService = BackgroundSurveySyncService.instance;
    });

    tearDown(() {
      // Clean up after each test
      syncManager.dispose();
      syncService.dispose();
    });

    test('BackgroundSyncManager should be a singleton', () {
      final instance1 = BackgroundSyncManager.instance;
      final instance2 = BackgroundSyncManager.instance;

      expect(instance1, equals(instance2));
    });

    test('BackgroundSurveySyncService should be a singleton', () {
      final instance1 = BackgroundSurveySyncService.instance;
      final instance2 = BackgroundSurveySyncService.instance;

      expect(instance1, equals(instance2));
    });

    test('SyncStatusInfo should provide correct status text', () {
      final statusInfo = SyncStatusInfo(
        isSyncing: false,
        isInitialized: true,
        lastSyncTime: DateTime.now().subtract(const Duration(minutes: 5)),
        lastSyncError: null,
        lastSyncCount: 10,
      );

      expect(statusInfo.statusText, contains('5m ago'));
      expect(statusInfo.statusColor, isNot(equals(Colors.red))); // Not red
    });

    test('SyncStatusInfo should show error status correctly', () {
      final statusInfo = SyncStatusInfo(
        isSyncing: false,
        isInitialized: true,
        lastSyncTime: null,
        lastSyncError: 'Network error',
        lastSyncCount: 0,
      );

      expect(statusInfo.statusText, equals('Sync failed'));
      // Check that the color is red (Colors.red)
      expect(statusInfo.statusColor, equals(Colors.red));
    });

    test('SyncProgress should calculate percentage correctly', () {
      final progress = SyncProgress(
        current: 25,
        total: 100,
        operation: 'Syncing data',
        percentage: 25,
      );

      expect(progress.percentage, equals(25));
      expect(progress.current, equals(25));
      expect(progress.total, equals(100));
      expect(progress.operation, equals('Syncing data'));
    });

    test('SyncProgress.fromMap should create correct instance', () {
      final map = {
        'current': 50,
        'total': 200,
        'operation': 'Uploading images',
        'percentage': 25,
      };

      final progress = SyncProgress.fromMap(map);

      expect(progress.current, equals(50));
      expect(progress.total, equals(200));
      expect(progress.operation, equals('Uploading images'));
      expect(progress.percentage, equals(25));
    });

    test('SyncStatus enum should have all required values', () {
      expect(SyncStatus.values.length, equals(4));
      expect(SyncStatus.values, contains(SyncStatus.idle));
      expect(SyncStatus.values, contains(SyncStatus.syncing));
      expect(SyncStatus.values, contains(SyncStatus.completed));
      expect(SyncStatus.values, contains(SyncStatus.error));
    });

    test('BackgroundSyncManager initial state should be correct', () {
      expect(syncManager.isSyncing, isFalse);
      expect(syncManager.isInitialized, isFalse);
      expect(syncManager.lastSyncTime, isNull);
      expect(syncManager.lastSyncError, isNull);
    });

    test('BackgroundSurveySyncService initial state should be correct', () {
      expect(syncService.isSyncing, isFalse);
      expect(syncService.isInitialized, isFalse);
    });

    // Note: Integration tests with actual Hive database and network calls
    // should be run separately as they require more setup and mocking
  });

  group('SyncStatusInfo Tests', () {
    test('should show "Not initialized" when not initialized', () {
      final statusInfo = SyncStatusInfo(
        isSyncing: false,
        isInitialized: false,
        lastSyncTime: null,
        lastSyncError: null,
        lastSyncCount: 0,
      );

      expect(statusInfo.statusText, equals('Not initialized'));
    });

    test('should show "Syncing..." when syncing', () {
      final statusInfo = SyncStatusInfo(
        isSyncing: true,
        isInitialized: true,
        lastSyncTime: null,
        lastSyncError: null,
        lastSyncCount: 0,
      );

      expect(statusInfo.statusText, equals('Syncing...'));
    });

    test('should show "Ready to sync" when initialized but never synced', () {
      final statusInfo = SyncStatusInfo(
        isSyncing: false,
        isInitialized: true,
        lastSyncTime: null,
        lastSyncError: null,
        lastSyncCount: 0,
      );

      expect(statusInfo.statusText, equals('Ready to sync'));
    });

    test('should show "Synced just now" for very recent sync', () {
      final statusInfo = SyncStatusInfo(
        isSyncing: false,
        isInitialized: true,
        lastSyncTime: DateTime.now().subtract(const Duration(seconds: 30)),
        lastSyncError: null,
        lastSyncCount: 5,
      );

      expect(statusInfo.statusText, equals('Synced just now'));
    });

    test('should show hours for old sync times', () {
      final statusInfo = SyncStatusInfo(
        isSyncing: false,
        isInitialized: true,
        lastSyncTime: DateTime.now().subtract(const Duration(hours: 2)),
        lastSyncError: null,
        lastSyncCount: 5,
      );

      expect(statusInfo.statusText, equals('Synced 2h ago'));
    });
  });
}
