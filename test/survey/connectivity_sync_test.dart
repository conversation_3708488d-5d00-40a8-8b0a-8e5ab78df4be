import 'package:flutter_test/flutter_test.dart';
import 'package:schnell_pole_installation/survey/connectivity_sync_service.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  group('ConnectivitySyncService Tests', () {
    late ConnectivitySyncService syncService;

    setUp(() {
      syncService = ConnectivitySyncService.instance;
    });

    tearDown(() {
      // Clean up after each test
      ConnectivitySyncService.resetInstance();
    });

    test('ConnectivitySyncService should be a singleton', () {
      final instance1 = ConnectivitySyncService.instance;
      final instance2 = ConnectivitySyncService.instance;
      expect(instance1, same(instance2));
    });

    test('ConnectivitySyncService initial state should be correct', () {
      expect(syncService.isSyncing, isFalse);
      expect(syncService.isInitialized, isFalse);
    });

    test('SyncProgress should calculate percentage correctly', () {
      final progress = SyncProgress(current: 25, total: 100, operation: 'test');
      expect(progress.percentage, 25.0);
    });

    test('SyncProgress.fromMap should create correct instance', () {
      final map = {
        'current': 50,
        'total': 200,
        'operation': 'uploading',
      };
      final progress = SyncProgress.fromMap(map);
      expect(progress.current, 50);
      expect(progress.total, 200);
      expect(progress.operation, 'uploading');
      expect(progress.percentage, 25.0);
    });

    test('SyncStatus enum should have all required values', () {
      expect(SyncStatus.values.length, 4);
      expect(SyncStatus.values, contains(SyncStatus.idle));
      expect(SyncStatus.values, contains(SyncStatus.syncing));
      expect(SyncStatus.values, contains(SyncStatus.completed));
      expect(SyncStatus.values, contains(SyncStatus.error));
    });

    test('Initialize should set isInitialized to true', () async {
      expect(syncService.isInitialized, isFalse);
      await syncService.initialize();
      expect(syncService.isInitialized, isTrue);
    });

    test('stopMonitoring should not throw when no monitoring is active', () {
      expect(() => syncService.stopMonitoring(), returnsNormally);
    });

    test('performManualSync should reset debounce timer', () async {
      await syncService.initialize();

      // This should work without throwing
      expect(() => syncService.performManualSync(), returnsNormally);
    });

    // Note: Actual connectivity monitoring and sync functionality tests would require
    // connectivity mocking and Hive database setup
  });
}
