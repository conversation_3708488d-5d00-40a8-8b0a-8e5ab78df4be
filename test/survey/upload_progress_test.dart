import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:schnell_pole_installation/survey/widgets/sync_status_indicator.dart';

void main() {
  group('Upload Progress Indicator Tests', () {
    testWidgets('UploadProgressIndicator should build without error', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: UploadProgressIndicator(),
          ),
        ),
      );

      // Should not throw any errors
      expect(find.byType(UploadProgressIndicator), findsOneWidget);
    });

    testWidgets('UploadProgressIndicator should show nothing when idle', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: UploadProgressIndicator(),
          ),
        ),
      );

      // Should show SizedBox.shrink when idle
      expect(find.byType(SizedBox), findsOneWidget);
    });

    testWidgets('UploadProgressIndicator should have correct icon size', (WidgetTester tester) async {
      const double testIconSize = 24.0;
      
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: UploadProgressIndicator(iconSize: testIconSize),
          ),
        ),
      );

      final uploadProgressIndicator = tester.widget<UploadProgressIndicator>(
        find.byType(UploadProgressIndicator),
      );
      
      expect(uploadProgressIndicator.iconSize, equals(testIconSize));
    });
  });
}
