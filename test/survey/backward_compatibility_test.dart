import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:schnell_pole_installation/survey/models/survey_image_model.dart';
import 'package:schnell_pole_installation/utils/image_migration_util.dart';

void main() {
  group('Backward Compatibility Tests', () {
    
    test('should handle legacy base64 data correctly', () {
      // Arrange - Create legacy data format (old app version)
      final legacyMap = {
        'fileName1': 'legacy_image1.jpg',
        'base64Image1': 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==', // 1x1 pixel PNG
        'isUploaded1': false,
        'fileName2': '',
        'base64Image2': '',
        'isUploaded2': false,
        'fileName3': '',
        'base64Image3': '',
        'isUploaded3': false,
        // Note: No filePath fields in legacy data
      };
      
      // Act - Load legacy data into new model
      final model = MultiCapturedImageModel.fromMap(legacyMap);
      
      // Assert - Should handle legacy data correctly
      expect(model.fileName1, equals('legacy_image1.jpg'));
      expect(model.base64Image1, isNotNull);
      expect(model.base64Image1!.isNotEmpty, isTrue);
      expect(model.filePath1, isEmpty); // No file path in legacy data
      expect(model.hasLegacyData, isTrue); // Should detect legacy data
      expect(model.isUploaded1, isFalse);
    });
    
    test('should handle new optimized data correctly', () {
      // Arrange - Create new data format (updated app version)
      final newMap = {
        'fileName1': 'optimized_image1.jpg',
        'filePath1': '/path/to/compressed/image1.jpg',
        'isUploaded1': false,
        'fileName2': '',
        'filePath2': '',
        'isUploaded2': false,
        'fileName3': '',
        'filePath3': '',
        'isUploaded3': false,
        // Note: No base64 fields in new data
      };
      
      // Act - Load new data into model
      final model = MultiCapturedImageModel.fromMap(newMap);
      
      // Assert - Should handle new data correctly
      expect(model.fileName1, equals('optimized_image1.jpg'));
      expect(model.filePath1, equals('/path/to/compressed/image1.jpg'));
      expect(model.base64Image1, isNull); // No base64 in new data
      expect(model.hasLegacyData, isFalse); // Should not detect legacy data
      expect(model.isUploaded1, isFalse);
    });
    
    test('should handle mixed data formats correctly', () {
      // Arrange - Create mixed data (during transition period)
      final mixedMap = {
        'fileName1': 'legacy_image1.jpg',
        'base64Image1': 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
        'filePath1': '', // Empty file path for legacy image
        'isUploaded1': false,
        'fileName2': 'optimized_image2.jpg',
        'base64Image2': null,
        'filePath2': '/path/to/compressed/image2.jpg', // New format
        'isUploaded2': false,
        'fileName3': '',
        'base64Image3': '',
        'filePath3': '',
        'isUploaded3': false,
      };
      
      // Act - Load mixed data into model
      final model = MultiCapturedImageModel.fromMap(mixedMap);
      
      // Assert - Should handle mixed data correctly
      expect(model.hasLegacyData, isTrue); // Has some legacy data
      expect(model.base64Image1, isNotNull); // Legacy format for image 1
      expect(model.filePath1, isEmpty);
      expect(model.filePath2, isNotEmpty); // New format for image 2
      expect(model.base64Image2, isNull);
    });
    
    test('should provide correct image data for upload', () {
      // Arrange - Create model with mixed formats
      final model = MultiCapturedImageModel(
        fileName1: 'legacy.jpg',
        filePath1: '', // Legacy: no file path
        base64Image1: 'base64data1',
        fileName2: 'optimized.jpg',
        filePath2: '/path/to/file2.jpg', // New: has file path
        base64Image2: null,
        fileName3: '',
        filePath3: '',
        base64Image3: null,
      );
      
      // Act & Assert - Should return correct data for each format
      expect(model.getImageData1(), equals('base64data1')); // Legacy: returns base64
      expect(model.getImageData2(), isNull); // New: returns null (will use file path)
      expect(model.getImageData3(), isNull); // Empty: returns null
    });
    
    test('should serialize and deserialize correctly', () {
      // Arrange - Create model with both formats
      final originalModel = MultiCapturedImageModel(
        fileName1: 'test1.jpg',
        filePath1: '/path/to/file1.jpg',
        base64Image1: 'base64data1',
        fileName2: 'test2.jpg',
        filePath2: '',
        base64Image2: 'base64data2',
        fileName3: '',
        filePath3: '',
        base64Image3: null,
      );
      
      // Act - Serialize to map and deserialize back
      final map = originalModel.toMap();
      final deserializedModel = MultiCapturedImageModel.fromMap(map);
      
      // Assert - Should preserve all data
      expect(deserializedModel.fileName1, equals(originalModel.fileName1));
      expect(deserializedModel.filePath1, equals(originalModel.filePath1));
      expect(deserializedModel.base64Image1, equals(originalModel.base64Image1));
      expect(deserializedModel.fileName2, equals(originalModel.fileName2));
      expect(deserializedModel.filePath2, equals(originalModel.filePath2));
      expect(deserializedModel.base64Image2, equals(originalModel.base64Image2));
      expect(deserializedModel.hasLegacyData, equals(originalModel.hasLegacyData));
    });
    
    test('should handle null and empty values gracefully', () {
      // Arrange - Create map with null/empty values
      final emptyMap = <String, dynamic>{
        'fileName1': null,
        'filePath1': null,
        'base64Image1': null,
        'isUploaded1': null,
        'fileName2': '',
        'filePath2': '',
        'base64Image2': '',
        'isUploaded2': null,
        // Missing fileName3, filePath3, base64Image3, isUploaded3
      };
      
      // Act - Load into model
      final model = MultiCapturedImageModel.fromMap(emptyMap);
      
      // Assert - Should handle gracefully with defaults
      expect(model.fileName1, equals(''));
      expect(model.filePath1, equals(''));
      expect(model.base64Image1, isNull);
      expect(model.isUploaded1, isFalse);
      expect(model.fileName2, equals(''));
      expect(model.filePath2, equals(''));
      expect(model.base64Image2, equals(''));
      expect(model.isUploaded2, isFalse);
      expect(model.fileName3, equals(''));
      expect(model.filePath3, equals(''));
      expect(model.base64Image3, isNull);
      expect(model.isUploaded3, isFalse);
      expect(model.hasLegacyData, isFalse);
    });
    
    test('should detect legacy data correctly in various scenarios', () {
      // Test case 1: Pure legacy data
      final legacyModel = MultiCapturedImageModel(
        fileName1: 'test.jpg',
        filePath1: '',
        base64Image1: 'base64data',
        fileName2: '',
        filePath2: '',
        base64Image2: null,
        fileName3: '',
        filePath3: '',
        base64Image3: null,
      );
      expect(legacyModel.hasLegacyData, isTrue);
      
      // Test case 2: Pure new data
      final newModel = MultiCapturedImageModel(
        fileName1: 'test.jpg',
        filePath1: '/path/to/file.jpg',
        base64Image1: null,
        fileName2: '',
        filePath2: '',
        base64Image2: null,
        fileName3: '',
        filePath3: '',
        base64Image3: null,
      );
      expect(newModel.hasLegacyData, isFalse);
      
      // Test case 3: Mixed data
      final mixedModel = MultiCapturedImageModel(
        fileName1: 'test1.jpg',
        filePath1: '/path/to/file1.jpg',
        base64Image1: null,
        fileName2: 'test2.jpg',
        filePath2: '',
        base64Image2: 'base64data',
        fileName3: '',
        filePath3: '',
        base64Image3: null,
      );
      expect(mixedModel.hasLegacyData, isTrue);
      
      // Test case 4: Empty data
      final emptyModel = MultiCapturedImageModel(
        fileName1: '',
        filePath1: '',
        base64Image1: null,
        fileName2: '',
        filePath2: '',
        base64Image2: null,
        fileName3: '',
        filePath3: '',
        base64Image3: null,
      );
      expect(emptyModel.hasLegacyData, isFalse);
    });
  });
}
